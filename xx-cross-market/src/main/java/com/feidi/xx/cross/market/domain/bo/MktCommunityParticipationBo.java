package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktCommunityParticipation;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动领取记录业务对象 mkt_community_participation
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktCommunityParticipation.class, reverseConvertGenerate = false)
public class MktCommunityParticipationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long activityId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long passengerId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 领取时间
     */
    @NotNull(message = "领取时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date receiveTime;

    /**
     * 状态：1成功，0失败
     */
    @NotBlank(message = "状态：1成功，0失败不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 失败原因，比如超过领取限制
     */
    @NotBlank(message = "失败原因，比如超过领取限制不能为空", groups = {AddGroup.class, EditGroup.class})
    private String reason;


}
