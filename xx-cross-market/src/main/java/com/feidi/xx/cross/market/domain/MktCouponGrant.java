package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 优惠券发放对象 mkt_coupon_grant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_coupon_grant")
public class MktCouponGrant extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 乘客
     */
    private Long passengerId;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动
     *
     * @deprecated 目前这个字段已废弃，千万别使用
     */
    @Deprecated
    private Long activityId;

    /**
     * 优惠卷
     */
    private Long couponId;

    /**
     * 优惠卷名
     */
    private String couponName;

    /**
     * 线路
     */
    private String lineId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 优惠卷编码
     */
    private String couponCode;

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    private String discountType;

    /**
     * 折扣额度
     */
    private Long quota;

    /**
     * 领取方式[PaidTypeEnum]
     */
    private String paidType;

    /**
     * 领取人类型
     */
    private String paidUserType;

    /**
     * 使用状态[CouponStatusEnum]
     */
    private String usingStatus;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 核销时间
     */
    private Date wipedTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    private String productScope;

    /**
     * 优惠券来源[UserCouponSourceEnum] 来源类型：1=活动，2=定向发放
     */
    private String sourceType;
    /**
     * 来源ID，如活动ID、发放表ID等
     */
    private Long sourceId;

    /**
     * 订单号
     */
    private String orderNo;

}
