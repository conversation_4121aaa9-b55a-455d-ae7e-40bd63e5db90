package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktCommunityParticipationBo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityParticipationVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 活动领取记录Service接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IMktCommunityParticipationService {

    /**
     * 查询活动领取记录
     *
     * @param id 主键
     * @return 活动领取记录
     */
    MktCommunityParticipationVo queryById(Long id);

    /**
     * 分页查询活动领取记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动领取记录分页列表
     */
    TableDataInfo<MktCommunityParticipationVo> queryPageList(MktCommunityParticipationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动领取记录列表
     *
     * @param bo 查询条件
     * @return 活动领取记录列表
     */
    List<MktCommunityParticipationVo> queryList(MktCommunityParticipationBo bo);

    /**
     * 新增活动领取记录
     *
     * @param bo 活动领取记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MktCommunityParticipationBo bo);

    /**
     * 查询每个活动参与人数
     *
     * @param campaignIds 活动ID集合
     * @return 活动ID到参与人数的映射
     */
    Map<Long, Long> queryParticipationCountByCampaignIds(Set<Long> campaignIds);
}
