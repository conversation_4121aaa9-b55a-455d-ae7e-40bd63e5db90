package com.feidi.xx.cross.market.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.market.domain.MktCommunityCampaign;
import com.feidi.xx.cross.market.domain.bo.MktCommunityDataRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityDataRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 社群活动主Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface MktCommunityCampaignMapper extends BaseMapperPlus<MktCommunityCampaign, MktCommunityCampaignVo> {

    /**
     * 分页查询社群活动数据记录
     * 以mkt_coupon_grant为主表，关联mkt_community_campaign和mkt_community_participation
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @return 数据记录分页列表
     */
    Page<MktCommunityDataRecordVo> selectDataRecordPage(Page<MktCommunityDataRecordVo> page, @Param("bo") MktCommunityDataRecordBo bo);

}
