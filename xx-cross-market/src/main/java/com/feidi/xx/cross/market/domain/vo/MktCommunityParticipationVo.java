package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.market.domain.MktCommunityParticipation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 活动领取记录视图对象 mkt_community_participation
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktCommunityParticipation.class)
public class MktCommunityParticipationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long passengerId;

    /**
     * 领取时间
     */
    @ExcelProperty(value = "领取时间")
    private Date receiveTime;

    /**
     * 状态：1成功，0失败
     */
    @ExcelProperty(value = "状态：1成功，0失败")
    private String status;

    /**
     * 失败原因，比如超过领取限制
     */
    @ExcelProperty(value = "失败原因，比如超过领取限制")
    private String reason;


}
