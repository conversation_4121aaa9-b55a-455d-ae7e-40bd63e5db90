package com.feidi.xx.cross.market.timer;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 社群活动更新状态定时器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MktCommunityCampaignUpdateStatusJob {

    @XxlJob("mktCommunityCampaignUpdateStatusJob")
    public void mktCommunityCampaignUpdateStatusJob() {
        log.info("社群活动更新状态定时器开始执行");
        //TODO 业务逻辑
        log.info("社群活动更新状态定时器执行结束");
    }
}
