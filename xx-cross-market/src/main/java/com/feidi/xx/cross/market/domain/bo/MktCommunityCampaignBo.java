package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.common.enums.market.CampaignStatusEnum;
import com.feidi.xx.cross.market.domain.MktCommunityCampaign;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 社群活动主业务对象 mkt_community_campaign
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktCommunityCampaign.class, reverseConvertGenerate = false)
public class MktCommunityCampaignBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String activityName;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     */
    private String status;

    /**
     * 分享标题
     */
    @NotBlank(message = "分享标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String shareTitle;

    /**
     * 领取限制 1:活动期间限领一次 2:每天限领一次
     *
     * @see com.feidi.xx.cross.common.enums.market.ReceiveLimitTypeEnum
     */
    @NotBlank(message = "领取限制 1:活动期间限领一次 2:每天限领一次不能为空", groups = {AddGroup.class, EditGroup.class})
    private String receiveLimitType;

    /**
     * 关联优惠券ID数组
     */
    @NotEmpty(message = "优惠券不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 10, message = "优惠券最多选择10张")
    private List<Long> couponIds;

    /**
     * 活动规则
     */
    @NotBlank(message = "活动规则不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ruleContent;


    /**
     * 查询 活动创建时间范围
     */
    private StartEndTime timeRangeForCreateTime;

    public void validate() {
        //必填，开始时间-结束时间，精确到天。结束时间必须大于等于今天
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("活动开始时间和结束时间不能为空");
        }
        if (startTime.after(endTime)) {
            throw new IllegalArgumentException("活动开始时间不能晚于结束时间");
        }
        if (endTime.before(new Date())) {
            throw new IllegalArgumentException("活动结束时间不能早于今天");
        }
        // 如果开始时间小于等于当前时间,状态为进行中
        if (startTime.before(new Date())) {
            this.setStatus(CampaignStatusEnum.ONGOING.getCode());
        }
        this.setStatus(CampaignStatusEnum.NOT_STARTED.getCode());
    }
}
