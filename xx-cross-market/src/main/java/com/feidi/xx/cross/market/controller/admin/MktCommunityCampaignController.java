package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.market.CampaignStatusEnum;
import com.feidi.xx.cross.market.domain.bo.MktCommunityCampaignBo;
import com.feidi.xx.cross.market.domain.bo.MktCommunityCampaignShareBo;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.service.IMktCommunityCampaignService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 社群活动
 * 前端访问路由地址为:/market/communityCampaign
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/communityCampaign")
public class MktCommunityCampaignController extends BaseController {

    private final IMktCommunityCampaignService mktCommunityCampaignService;

    /**
     * 查询社群活动列表
     */
    @SaCheckPermission("market:communityCampaign:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktCommunityCampaignVo> list(MktCommunityCampaignBo bo, PageQuery pageQuery) {
        return mktCommunityCampaignService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出社群活动列表
     */
    @SaCheckPermission("market:communityCampaign:export")
    @Log(title = "社群活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Download(name = "社群活动记录", module = ModuleConstants.MARKET, mode = "no")
    public Object export(@RequestBody MktCommunityCampaignBo bo, HttpServletResponse response) {
        List<MktCommunityCampaignVo> list = mktCommunityCampaignService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "社群活动记录", MktCommunityCampaignVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取社群活动详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:communityCampaign:query")
    @GetMapping("/{id}")
    public R<MktCommunityCampaignVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long id) {
        return R.ok(mktCommunityCampaignService.queryById(id));
    }

    /**
     * 新增社群活动
     */
    @SaCheckPermission("market:communityCampaign:add")
    @Log(title = "社群活动", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktCommunityCampaignBo bo) {
        bo.validate();
        return toAjax(mktCommunityCampaignService.insertByBo(bo));
    }

    /**
     * 修改社群活动
     */
    @SaCheckPermission("market:communityCampaign:edit")
    @Log(title = "社群活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktCommunityCampaignBo bo) {
        bo.validate();
        return toAjax(mktCommunityCampaignService.updateByBo(bo));
    }

    /**
     * 删除社群活动
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:communityCampaign:remove")
    @Log(title = "社群活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktCommunityCampaignService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 修改活动状态
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("market:communityCampaign:updateStatus")
    @Log(title = "社群活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody MktCommunityCampaignBo bo) {
        Assert.notNull(bo.getId(), "活动ID不能为空");
        Assert.notNull(bo.getStatus(), "活动状态不能为空");
        CampaignStatusEnum.getByCodeThrow(bo.getStatus());
        mktCommunityCampaignService.updateStatus(bo);
        return R.ok();
    }

    /**
     * 生成分享二维码
     * 分享（小程序二维码）
     *
     * @param id 活动ID
     * @return 二维码地址
     */
    @SaCheckPermission("market:communityCampaign:share")
    @GetMapping("/share/{id}")
    public R<String> share(@PathVariable Long id, @Validated MktCommunityCampaignShareBo bo) {
        Assert.notNull(id, "活动ID不能为空");
        String url = mktCommunityCampaignService.share(id, bo);
        return R.ok(url);
    }

    /**
     * 查询数据记录列表
     * 查询活动参与记录和优惠券发放记录的关联数据
     */
    @SaCheckPermission("market:communityCampaign:dataList")
    @GetMapping("/dataList")
    @Enum2TextAspect
    public TableDataInfo<MktCouponGrantVo> dataList(MktCouponGrantBo bo, PageQuery pageQuery) {
        return mktCommunityCampaignService.queryDataList(bo, pageQuery);
    }

    /**
     * 导出数据记录列表
     */
    @SaCheckPermission("market:communityCampaign:dataExport")
    @Log(title = "社群活动数据记录", businessType = BusinessType.EXPORT)
    @PostMapping("/dataExport")
    @Download(name = "社群活动数据记录", module = ModuleConstants.MARKET, mode = "no")
    public Object dataExport(@RequestBody MktCouponGrantBo bo, HttpServletResponse response) {
        List<MktCouponGrantVo> list = mktCommunityCampaignService.queryDataExportList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "社群活动数据记录", MktCouponGrantVo.class, outputStream);
        return outputStream.toByteArray();
    }
}
