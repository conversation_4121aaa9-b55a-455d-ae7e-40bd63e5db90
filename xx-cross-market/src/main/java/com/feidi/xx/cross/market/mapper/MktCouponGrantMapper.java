package com.feidi.xx.cross.market.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.bo.CouponQueryCountBo;
import com.feidi.xx.cross.market.domain.bo.TwoNumQueryCountBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 优惠券发放Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
public interface MktCouponGrantMapper extends BaseMapperPlus<MktCouponGrant, MktCouponGrantVo> {

    default boolean revoke(UserCouponSourceEnum userCouponSourceEnum, Long id, Long couponId) {
        var updateWrapper = Wrappers.<MktCouponGrant>lambdaUpdate()
                .eq(MktCouponGrant::getCouponId, couponId)
                .eq(MktCouponGrant::getSourceType, userCouponSourceEnum.getCode())
                .eq(MktCouponGrant::getSourceId, id)
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode())
                .set(MktCouponGrant::getUsingStatus, CouponStatusEnum.INVALID.getCode());
        return this.update(updateWrapper) > 0;
    }

    /**
     * 根据活动ID查询优惠券发放数量
     * 每个优惠券领取数量
     *
     * @param activityIds
     * @param userCouponSourceEnum
     * @return
     */
    List<CouponQueryCountBo> selectCouponGrantCountByActivityIds(@Param("activityIds") List<Long> activityIds,
                                                                 @Param("sourceEnum") UserCouponSourceEnum userCouponSourceEnum);


    /**
     * 根据活动ID查询优惠券使用情况
     *
     * @param activityId           活动ID
     * @param userCouponSourceEnum 优惠券来源
     * @param usingStatus          优惠券使用状态
     * @return
     */
    List<TwoNumQueryCountBo> selectActivityUsageStats(@Param("activityId") Long activityId,
                                                      @Param("sourceEnum") UserCouponSourceEnum userCouponSourceEnum,
                                                      @Param("usingStatus") CouponStatusEnum usingStatus);
}
