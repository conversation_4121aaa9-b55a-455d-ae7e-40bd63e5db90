package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.cross.common.enums.market.ReceiveLimitTypeEnum;
import com.feidi.xx.cross.market.domain.MktCommunityCampaign;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 社群活动主视图对象 mkt_community_campaign
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktCommunityCampaign.class)
public class MktCommunityCampaignVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     */
    @ExcelProperty(value = "活动状态 0:待开始 1:进行中 2:已结束")
    private String status;

    /**
     * 分享标题
     */
    @ExcelProperty(value = "分享标题")
    private String shareTitle;

    /**
     * 领取限制 1:活动期间限领一次 2:每天限领一次
     *
     * @see com.feidi.xx.cross.common.enums.market.ReceiveLimitTypeEnum
     */
    @ExcelProperty(value = "领取限制 1:活动期间限领一次 2:每天限领一次")
    @Enum2Text(enumClass = ReceiveLimitTypeEnum.class)
    private String receiveLimitType;

    /**
     * 关联优惠券ID数组
     */
    private List<Long> couponIds = new ArrayList<>();
    /**
     * 优惠券列表
     */
    private List<MktCouponVo> couponVos = new ArrayList<>();
    /**
     * 活动规则
     */
    @ExcelProperty(value = "活动规则")
    private String ruleContent;

    /**
     * 优惠券余量
     */
    private Integer couponStock = 0;

    /**
     * 优惠券领取数量
     */
    private Long couponReceiveCount = 0L;

    /**
     * 参与人数
     */
    private Long participationCount = 0L;

    /**
     * 使用人数
     */
    private Long useCount = 0L;

    /**
     * 优惠券使用数量
     */
    private Long couponUseCount = 0L;
}
