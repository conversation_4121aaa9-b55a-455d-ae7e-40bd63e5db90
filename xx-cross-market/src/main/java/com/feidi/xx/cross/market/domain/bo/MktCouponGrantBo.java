package com.feidi.xx.cross.market.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优惠券发放业务对象 mkt_coupon_grant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktCouponGrant.class, reverseConvertGenerate = false)
public class MktCouponGrantBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 乘客列表
     */
    private List<String> passengerPhones;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 手机尾号
     */
    private String phoneEnd;

    /**
     * 活动
     */
    private Long activityId;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 优惠卷
     */
    private Long couponId;

    /**
     * 优惠卷ids
     */
    private List<Long> couponIds;

    /**
     * 优惠卷名
     */
    private String couponName;

    /**
     * 线路
     */
    private String lineId;

    /**
     * 优惠卷编码
     */
    private String couponCode;

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 订单预估价
     */
    private BigDecimal orderPrice;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    private String discountType;

    /**
     * 折扣额度
     */
    private Long quota;

    /**
     * 领取方式[PaidTypeEnum]
     */
    private String paidType;

    /**
     * 领取人类型
     */
    private String paidUserType;

    /**
     * 使用状态
     * CouponStatusEnum
     */
    private String usingStatus;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 核销时间
     */
    private Date wipedTime;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    private String productScope;

    /**
     * 是否乘客端查询
     */
    private Boolean passengerQuery = false;

    /**
     *  优惠券来源[UserCouponSourceEnum] 来源类型：1=活动，2=定向发放
     */
    private String sourceType;
    /**
     * 来源ID，如活动ID、发放表ID等
     */
    private Long sourceId;
}
