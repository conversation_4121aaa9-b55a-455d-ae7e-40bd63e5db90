package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktCommunityCampaignBo;
import com.feidi.xx.cross.market.domain.bo.MktCommunityCampaignShareBo;
import com.feidi.xx.cross.market.domain.bo.MktCommunityDataRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityDataRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 社群活动主Service接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IMktCommunityCampaignService {

    /**
     * 查询社群活动主
     *
     * @param id 主键
     * @return 社群活动主
     */
    MktCommunityCampaignVo queryById(Long id);

    /**
     * 分页查询社群活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 社群活动主分页列表
     */
    TableDataInfo<MktCommunityCampaignVo> queryPageList(MktCommunityCampaignBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的社群活动主列表
     *
     * @param bo 查询条件
     * @return 社群活动主列表
     */
    List<MktCommunityCampaignVo> queryList(MktCommunityCampaignBo bo);

    /**
     * 新增社群活动主
     *
     * @param bo 社群活动主
     * @return 是否新增成功
     */
    Boolean insertByBo(MktCommunityCampaignBo bo);

    /**
     * 修改社群活动主
     *
     * @param bo 社群活动主
     * @return 是否修改成功
     */
    Boolean updateByBo(MktCommunityCampaignBo bo);

    /**
     * 校验并批量删除社群活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 修改活动状态
     *
     * @param bo 包含id和status的业务对象
     */
    void updateStatus(MktCommunityCampaignBo bo);

    /**
     * 生成分享二维码
     * 分享（小程序二维码）
     *
     * @param id 活动ID
     * @param bo 分享参数
     * @return 二维码地址
     */
    String share(Long id, MktCommunityCampaignShareBo bo);

    /**
     * 分页查询数据记录列表
     * 以mkt_coupon_grant为主表，关联mkt_community_campaign和mkt_community_participation
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 数据记录分页列表
     */
    TableDataInfo<MktCommunityDataRecordVo> queryDataRecordList(MktCommunityDataRecordBo bo, PageQuery pageQuery);

    /**
     * 查询数据记录导出列表
     * 以mkt_coupon_grant为主表，关联mkt_community_campaign和mkt_community_participation
     *
     * @param bo 查询条件
     * @return 数据记录列表
     */
    List<MktCommunityDataRecordVo> queryDataRecordExportList(MktCommunityDataRecordBo bo);
}
