package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 社群活动数据记录查询业务对象
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MktCommunityDataRecordBo extends BaseEntity {

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 优惠券状态
     *
     * @see com.feidi.xx.cross.common.enums.market.CouponStatusEnum
     */
    private String usingStatus;

    /**
     * 领取时间范围
     */
    private StartEndTime receiveTimeRange;
    
}
