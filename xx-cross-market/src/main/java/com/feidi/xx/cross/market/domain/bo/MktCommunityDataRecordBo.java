package com.feidi.xx.cross.market.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 社群活动数据记录查询业务对象
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MktCommunityDataRecordBo extends BaseEntity {

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 优惠券状态
     */
    private String usingStatus;

    /**
     * 领取开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveStartTime;

    /**
     * 领取结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveEndTime;

    /**
     * 核销开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wipedStartTime;

    /**
     * 核销结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wipedEndTime;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 线路ID
     */
    private String lineId;

    /**
     * 产品范围
     */
    private String productScope;
}
