package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 活动领取记录对象 mkt_community_participation
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_community_participation")
public class MktCommunityParticipation extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID
     */
    private Long passengerId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 领取时间
     */
    private Date receiveTime;

    /**
     * 状态：1成功，0失败
     */
    private String status;

    /**
     * 失败原因，比如超过领取限制
     */
    private String reason;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
