package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.DiscountTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 社群活动数据记录视图对象
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@ExcelIgnoreUnannotated
public class MktCommunityDataRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 优惠券发放记录ID
     */
    private Long couponGrantId;

    /**
     * 参与记录ID
     */
    private Long participationId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 优惠券名称
     */
    @ExcelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 乘客手机号
     */
    @ExcelProperty(value = "乘客手机号")
    private String passengerPhone;

    /**
     * 适用范围 - 产品范围
     */
    @ExcelProperty(value = "适用范围")
    private String productScope;

    /**
     * 优惠类型
     */
    @Enum2Text(enumClass = DiscountTypeEnum.class)
    private String discountType;

    /**
     * 折扣额度
     */
    private Long quota;

    /**
     * 优惠信息（组合显示）
     */
    @ExcelProperty(value = "优惠信息")
    private String discountInfo;

    /**
     * 优惠券状态
     */
    @ExcelProperty(value = "优惠券状态")
    @Enum2Text(enumClass = CouponStatusEnum.class)
    private String usingStatus;

    /**
     * 领取时间
     */
    @ExcelProperty(value = "领取时间")
    private Date receiveTime;

    /**
     * 核销时间
     */
    @ExcelProperty(value = "核销时间")
    private Date wipedTime;

    /**
     * 核销订单号
     */
    @ExcelProperty(value = "核销订单号")
    private String orderNo;

    /**
     * 优惠券开始时间
     */
    private Date startTime;

    /**
     * 优惠券结束时间
     */
    private Date endTime;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 线路ID
     */
    private String lineId;

    /**
     * 乘客ID
     */
    private Long passengerId;

    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 参与状态（来自participation表）
     */
    private String participationStatus;

    /**
     * 参与失败原因（来自participation表）
     */
    private String participationReason;

    /**
     * 获取优惠信息的组合显示
     */
    public String getDiscountInfo() {
        if (discountType == null || quota == null) {
            return "";
        }

        if ("1".equals(discountType)) { // 立减
            return "立减" + (quota / 100.0) + "元";
        } else if ("2".equals(discountType)) { // 折扣
            return (quota / 10.0) + "折";
        } else if ("3".equals(discountType)) { // 满减
            return "满减" + (quota / 100.0) + "元";
        }

        return quota + "";
    }
}
