package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 社群活动主对象 mkt_community_campaign
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mkt_community_campaign", autoResultMap = true)
public class MktCommunityCampaign extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     *
     * @see com.feidi.xx.cross.common.enums.market.CampaignStatusEnum
     */
    private String status;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 领取限制 1:活动期间限领一次 2:每天限领一次
     *
     * @see com.feidi.xx.cross.common.enums.market.ReceiveLimitTypeEnum
     */
    private String receiveLimitType;

    /**
     * 关联优惠券ID数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> couponIds;

    /**
     * 活动规则
     */
    private String ruleContent;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
