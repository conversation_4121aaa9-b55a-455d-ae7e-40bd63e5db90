package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktCommunityParticipation;
import com.feidi.xx.cross.market.domain.bo.MktCommunityParticipationBo;
import com.feidi.xx.cross.market.domain.vo.MktCommunityParticipationVo;
import com.feidi.xx.cross.market.mapper.MktCommunityParticipationMapper;
import com.feidi.xx.cross.market.service.IMktCommunityParticipationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 活动领取记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@RequiredArgsConstructor
@Service
public class MktCommunityParticipationServiceImpl implements IMktCommunityParticipationService {

    private final MktCommunityParticipationMapper baseMapper;

    /**
     * 查询活动领取记录
     *
     * @param id 主键
     * @return 活动领取记录
     */
    @Override
    public MktCommunityParticipationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询活动领取记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动领取记录分页列表
     */
    @Override
    public TableDataInfo<MktCommunityParticipationVo> queryPageList(MktCommunityParticipationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktCommunityParticipation> lqw = buildQueryWrapper(bo);
        Page<MktCommunityParticipationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动领取记录列表
     *
     * @param bo 查询条件
     * @return 活动领取记录列表
     */
    @Override
    public List<MktCommunityParticipationVo> queryList(MktCommunityParticipationBo bo) {
        LambdaQueryWrapper<MktCommunityParticipation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktCommunityParticipation> buildQueryWrapper(MktCommunityParticipationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktCommunityParticipation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getActivityId() != null, MktCommunityParticipation::getActivityId, bo.getActivityId());
        lqw.eq(bo.getPassengerId() != null, MktCommunityParticipation::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getReceiveTime() != null, MktCommunityParticipation::getReceiveTime, bo.getReceiveTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktCommunityParticipation::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), MktCommunityParticipation::getReason, bo.getReason());
        return lqw;
    }

    /**
     * 新增活动领取记录
     *
     * @param bo 活动领取记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktCommunityParticipationBo bo) {
        MktCommunityParticipation add = MapstructUtils.convert(bo, MktCommunityParticipation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    public void validEntityBeforeSave(MktCommunityParticipation entity) {
    }

    @Override
    public Map<Long, Long> queryParticipationCountByCampaignIds(Set<Long> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Map.of();
        }
        LambdaQueryWrapper<MktCommunityParticipation> lqw = Wrappers.lambdaQuery();
        lqw.in(MktCommunityParticipation::getActivityId, campaignIds);
        lqw.select(MktCommunityParticipation::getActivityId, MktCommunityParticipation::getPassengerId);
        return baseMapper.selectList(lqw).stream().collect(Collectors.groupingBy(MktCommunityParticipation::getActivityId, Collectors.counting()));
    }
}
