package com.feidi.xx.cross.market.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 乘客端-乘客推乘客活动
 * 前端访问路由地址为:/market/br/communityCampaign
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/communityCampaign")
public class MbrCommunityCampaignController {
    //TODO R 查询活动以及活动卡券（如果已领取，则展示已领取卡券信息）
    //TODO R 领取卡券
}
