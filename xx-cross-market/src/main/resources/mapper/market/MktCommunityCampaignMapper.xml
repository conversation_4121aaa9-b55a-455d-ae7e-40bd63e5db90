<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktCommunityCampaignMapper">

    <!-- 查询社群活动数据记录 -->
    <select id="selectDataRecordPage" resultType="com.feidi.xx.cross.market.domain.vo.MktCommunityDataRecordVo">
        SELECT
            cg.id as couponGrantId,
            cp.id as participationId,
            cc.id as activityId,
            cc.activity_name as activityName,
            cg.coupon_name as couponName,
            cp.phone as passengerPhone,
            cg.city_code as cityCode,
            cg.line_id as lineId,
            cg.product_scope as productScope,
            cg.discount_type as discountType,
            cg.quota as quota,
            cg.using_status as usingStatus,
            cp.receive_time as receiveTime,
            cg.wiped_time as wipedTime,
            cg.order_no as orderNo,
            cg.start_time as startTime,
            cg.end_time as endTime,
            cg.passenger_id as passengerId,
            cg.coupon_code as couponCode,
            cp.status as participationStatus,
            cp.reason as participationReason
        FROM mkt_coupon_grant cg
        LEFT JOIN mkt_community_campaign cc ON cg.source_id = cc.id
        LEFT JOIN mkt_community_participation cp ON cg.source_id = cp.activity_id AND cg.passenger_id = cp.passenger_id
        WHERE cg.source_type = '4'
        AND cg.del_flag = '0'
        AND (cc.del_flag = '0' OR cc.del_flag IS NULL)
        AND (cp.del_flag = '0' OR cp.del_flag IS NULL)
        ${ew.customSqlSegment}
    </select>

</mapper>
