<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktCouponGrantMapper">

    <select id="selectCouponGrantCountByActivityIds" resultType="com.feidi.xx.cross.market.domain.bo.CouponQueryCountBo">
        SELECT
        source_id       AS groupId,
        coupon_id       AS couponId,
        COUNT(*)        AS num
        FROM mkt_coupon_grant
        WHERE source_type = #{sourceEnum.code}
        <if test="activityIds != null and activityIds.size > 0">
            AND source_id IN
            <foreach collection="activityIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY source_id, coupon_id
        ORDER BY source_id, coupon_id
    </select>

    <select id="selectActivityUsageStats" resultType="com.feidi.xx.cross.market.domain.bo.TwoNumQueryCountBo">
        SELECT
            source_id               AS groupId,
            COUNT(DISTINCT passenger_id) AS num,
            COUNT(*)                AS num2
        FROM mkt_coupon_grant
        WHERE source_type = #{sourceEnum.code}
          AND source_id = #{activityId}
          AND using_status =  #{usingStatus.code}
        GROUP BY source_id
    </select>
</mapper>
