# 社群活动数据记录接口说明

## 接口概述

根据图片中显示的数据记录表格需求，创建了查询社群活动数据记录的接口，主要用于查询活动参与记录和优惠券发放记录的关联数据。

## 新增接口

### 1. 查询数据记录列表

**接口地址：** `GET /communityCampaign/dataList`

**权限：** `market:communityCampaign:dataList`

**请求参数：**
- `activityName` (String, 可选): 活动名称，支持模糊查询
- `passengerPhone` (String, 可选): 乘客手机号，支持模糊查询
- `couponName` (String, 可选): 优惠券名称，支持模糊查询
- `usingStatus` (String, 可选): 优惠券状态
- `startTime` (Date, 可选): 领取开始时间
- `endTime` (Date, 可选): 领取结束时间
- 分页参数：`pageNum`, `pageSize`, `orderByColumn`, `isAsc`

**响应数据：**
返回 `TableDataInfo<MktCouponGrantVo>` 格式的分页数据，包含以下字段：
- `id`: 记录ID
- `activityName`: 活动名称
- `couponName`: 优惠券名称
- `passengerPhone`: 乘客手机号
- `phoneEnd`: 手机尾号
- `discountType`: 优惠类型
- `quota`: 折扣额度
- `usingStatus`: 优惠券状态
- `startTime`: 开始时间
- `endTime`: 结束时间
- `wipedTime`: 核销时间
- `orderNo`: 核销订单号
- `createTime`: 领取时间
- `cityCode`: 城市编码
- `lineId`: 线路ID
- `productScope`: 产品范围

### 2. 导出数据记录列表

**接口地址：** `POST /communityCampaign/dataExport`

**权限：** `market:communityCampaign:dataExport`

**请求参数：** 与查询接口相同，但通过POST请求体传递

**响应：** Excel文件下载

## 实现细节

### 1. 数据来源
- 主要查询 `mkt_coupon_grant` 表中 `source_type = '4'` (社群活动) 的记录
- 通过 `source_id` 关联 `mkt_community_campaign` 表获取活动名称
- 通过 `passenger_id` 关联乘客信息获取手机号等信息

### 2. 查询逻辑
- 活动名称查询：先根据活动名称模糊查询活动表，获取活动ID列表，再查询优惠券发放记录
- 手机号查询：利用现有的 `phoneEnd` 字段进行模糊查询
- 自动设置 `sourceType = '4'` 限定为社群活动数据

### 3. 数据填充
- 活动名称通过 `setActivityNameAndDirectGrantName` 方法自动填充
- 乘客信息通过远程服务调用获取
- 城市和线路名称通过工具类自动设置

## 数据库字段映射

| 界面显示字段 | 数据库字段 | 说明 |
|------------|-----------|------|
| 优惠券名称 | coupon_name | 优惠券名称 |
| 乘客手机号 | passenger_phone | 通过passenger_id关联获取 |
| 适用范围 | city_code, line_id, product_scope | 城市、线路、产品范围 |
| 优惠信息 | discount_type, quota | 优惠类型和折扣额度 |
| 优惠券状态 | using_status | 使用状态 |
| 核销时间 | wiped_time | 核销时间 |
| 核销订单号 | order_no | 订单号 |
| 领取时间 | create_time | 创建时间 |

## 权限配置

需要在权限管理中添加以下权限：
- `market:communityCampaign:dataList` - 查询数据记录列表
- `market:communityCampaign:dataExport` - 导出数据记录

## 前端集成

前端可以参考现有的社群活动列表页面，添加"数据记录"标签页或独立页面，使用相同的分页和查询组件。

## 注意事项

1. 该接口只查询社群活动相关的数据，通过 `sourceType = '4'` 进行过滤
2. 活动名称查询会先查询活动表，如果没有匹配的活动会返回空结果
3. 手机号查询支持模糊匹配，可以输入完整手机号或部分号码
4. 导出功能使用现有的Excel导出框架，支持大数据量导出
