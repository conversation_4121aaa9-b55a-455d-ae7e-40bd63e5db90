# 社群活动数据记录接口说明

## 接口概述

根据图片中显示的数据记录表格需求，创建了查询社群活动数据记录的接口。采用以`mkt_coupon_grant`为主表，关联`mkt_community_campaign`（活动主表）和`mkt_community_participation`（参与记录表）的方案。

## 数据表关系

- **主表**: `mkt_coupon_grant` (优惠券发放记录)
- **关联表1**: `mkt_community_campaign` (社群活动主表) - 通过 `source_id = activity_id` 关联
- **关联表2**: `mkt_community_participation` (活动参与记录) - 通过 `source_id = activity_id AND passenger_id = passenger_id` 关联

## 新增接口

### 1. 查询数据记录列表

**接口地址：** `GET /communityCampaign/dataList`

**权限：** `market:communityCampaign:dataList`

**请求参数：** (使用 `MktCommunityDataRecordBo`)
- `activityName` (String, 可选): 活动名称，支持模糊查询
- `passengerPhone` (String, 可选): 乘客手机号，支持模糊查询
- `couponName` (String, 可选): 优惠券名称，支持模糊查询
- `usingStatus` (String, 可选): 优惠券状态
- `receiveStartTime` (Date, 可选): 领取开始时间
- `receiveEndTime` (Date, 可选): 领取结束时间
- `wipedStartTime` (Date, 可选): 核销开始时间
- `wipedEndTime` (Date, 可选): 核销结束时间
- `cityCode` (String, 可选): 城市编码
- `lineId` (String, 可选): 线路ID
- `productScope` (String, 可选): 产品范围
- 分页参数：`pageNum`, `pageSize`, `orderByColumn`, `isAsc`

**响应数据：**
返回 `TableDataInfo<MktCommunityDataRecordVo>` 格式的分页数据，包含以下字段：
- `couponGrantId`: 优惠券发放记录ID
- `participationId`: 参与记录ID
- `activityId`: 活动ID
- `activityName`: 活动名称
- `couponName`: 优惠券名称
- `passengerPhone`: 乘客手机号（来自participation表的phone字段）
- `cityNames`: 适用城市名称
- `lineNames`: 适用线路名称
- `productScope`: 产品范围
- `discountType`: 优惠类型
- `quota`: 折扣额度
- `discountInfo`: 优惠信息（组合显示）
- `usingStatus`: 优惠券状态
- `receiveTime`: 领取时间
- `wipedTime`: 核销时间
- `orderNo`: 核销订单号
- `participationStatus`: 参与状态
- `participationReason`: 参与失败原因

### 2. 导出数据记录列表

**接口地址：** `POST /communityCampaign/dataExport`

**权限：** `market:communityCampaign:dataExport`

**请求参数：** 与查询接口相同，但通过POST请求体传递

**响应：** Excel文件下载

## 实现细节

### 1. 核心SQL查询
```sql
SELECT
    cg.id as couponGrantId,
    cp.id as participationId,
    cc.id as activityId,
    cc.activity_name as activityName,
    cg.coupon_name as couponName,
    cp.phone as passengerPhone,
    cg.city_code as cityCode,
    cg.line_id as lineId,
    cg.product_scope as productScope,
    cg.discount_type as discountType,
    cg.quota as quota,
    cg.using_status as usingStatus,
    cp.receive_time as receiveTime,
    cg.wiped_time as wipedTime,
    cg.order_no as orderNo,
    cp.status as participationStatus,
    cp.reason as participationReason
FROM mkt_coupon_grant cg
LEFT JOIN mkt_community_campaign cc ON cg.source_id = cc.id
LEFT JOIN mkt_community_participation cp ON cg.source_id = cp.activity_id AND cg.passenger_id = cp.passenger_id
WHERE cg.source_type = '4'
AND cg.del_flag = '0'
AND (cc.del_flag = '0' OR cc.del_flag IS NULL)
AND (cp.del_flag = '0' OR cp.del_flag IS NULL)
```

### 2. 查询逻辑
- 以 `mkt_coupon_grant` 为主表，确保所有优惠券发放记录都能被查询到
- 通过 `source_type = '4'` 限定为社群活动数据
- 左连接活动主表获取活动信息
- 左连接参与记录表获取参与详情和手机号
- 支持多种条件的模糊查询和精确查询

### 3. 数据处理
- 乘客手机号直接从 `mkt_community_participation.phone` 字段获取
- 城市和线路名称通过编码转换为名称显示
- 优惠信息通过 `getDiscountInfo()` 方法组合显示
- 支持按领取时间倒序排列

## 数据库字段映射

| 界面显示字段 | 数据库字段 | 来源表 | 说明 |
|------------|-----------|--------|------|
| 优惠券名称 | coupon_name | mkt_coupon_grant | 优惠券名称 |
| 乘客手机号 | phone | mkt_community_participation | 新增的手机号字段 |
| 活动名称 | activity_name | mkt_community_campaign | 活动名称 |
| 适用范围 | city_code, line_id, product_scope | mkt_coupon_grant | 城市、线路、产品范围 |
| 优惠信息 | discount_type, quota | mkt_coupon_grant | 优惠类型和折扣额度 |
| 优惠券状态 | using_status | mkt_coupon_grant | 使用状态 |
| 核销时间 | wiped_time | mkt_coupon_grant | 核销时间 |
| 核销订单号 | order_no | mkt_coupon_grant | 订单号 |
| 领取时间 | receive_time | mkt_community_participation | 参与记录的领取时间 |
| 参与状态 | status | mkt_community_participation | 参与成功/失败状态 |
| 失败原因 | reason | mkt_community_participation | 参与失败原因 |

## 权限配置

需要在权限管理中添加以下权限：
- `market:communityCampaign:dataList` - 查询数据记录列表
- `market:communityCampaign:dataExport` - 导出数据记录

## 前端集成

前端可以参考现有的社群活动列表页面，添加"数据记录"标签页或独立页面，使用相同的分页和查询组件。

## 新增的类文件

### 1. BO类
- `MktCommunityDataRecordBo` - 查询条件业务对象

### 2. VO类
- `MktCommunityDataRecordVo` - 数据记录视图对象

### 3. Mapper方法
- `MktCommunityCampaignMapper.selectDataRecordPage()` - 分页查询数据记录

### 4. Service方法
- `IMktCommunityCampaignService.queryDataRecordList()` - 分页查询
- `IMktCommunityCampaignService.queryDataRecordExportList()` - 导出查询

## 注意事项

1. 该接口基于三表关联查询，性能较好且数据完整
2. 乘客手机号直接从 `mkt_community_participation.phone` 字段获取，需要确保该字段已正确填充
3. 支持多种查询条件的组合使用
4. 左连接确保即使参与记录缺失也能查询到优惠券发放记录
5. 导出功能支持大数据量，建议添加适当的查询条件限制
6. 城市和线路名称转换逻辑可根据实际需求完善
