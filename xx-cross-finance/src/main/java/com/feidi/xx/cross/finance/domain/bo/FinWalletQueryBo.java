package com.feidi.xx.cross.finance.domain.bo;

import lombok.Data;

import java.util.List;

@Data
public class FinWalletQueryBo {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机ids
     */
    private List<Long> driverIds;

    /**
     * 综合搜索
     */
    private String unionId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商ids
     */
    private List<Long> agentIds;

    /**
     * 账户状态 {@link com.feidi.xx.common.core.enums.UserStatusEnum}
     */
    private String status;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 开始更新时间
     */
    private String startUpdateTime;

    /**
     * 结束更新时间
     */
    private String endUpdateTime;

    /**
     * 创建时间开始
     */
    private String startCreateTime;

    /**
     * 创建时间结束
     */
    private String endCreateTime;

}
