package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.MoneyConvertUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.payment.common.enums.TransferErrorEnum;
import com.feidi.xx.common.payment.config.AliPayConfig;
import com.feidi.xx.common.payment.domain.trans.bo.TransferBo;
import com.feidi.xx.common.payment.domain.trans.vo.TransferVo;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.payment.mq.PaymentRecordProducer;
import com.feidi.xx.common.payment.strategy.ITransService;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.finance.FinanceCacheConstants;
import com.feidi.xx.cross.common.constant.finance.FinanceConstants;
import com.feidi.xx.cross.common.enums.finance.*;
import com.feidi.xx.cross.common.enums.order.OrderFlowTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.finance.common.validate.ReviewPassGroup;
import com.feidi.xx.cross.finance.common.validate.ReviewRejectGroup;
import com.feidi.xx.cross.finance.domain.*;
import com.feidi.xx.cross.finance.domain.bo.FinCashApplyBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashReviewBo;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletVo;
import com.feidi.xx.cross.finance.mapper.*;
import com.feidi.xx.cross.finance.mq.AutoPaymentEvent;
import com.feidi.xx.cross.finance.mq.AutoPaymentProducer;
import com.feidi.xx.cross.finance.service.IFinCashService;
import com.feidi.xx.cross.finance.service.IFinConfigService;
import com.feidi.xx.cross.finance.service.helper.WalletHelper;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverAccountService;
import com.feidi.xx.cross.power.api.RemoteDriverConsentLogService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverAccountVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.push.common.constants.VoiceConstant;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.mq.PushMsgProducer;
import com.feidi.xx.system.api.RemoteConfigService;
import com.feidi.xx.system.api.RemoteDictService;
import com.feidi.xx.system.api.RemoteUserService;
import com.feidi.xx.system.api.domain.vo.RemoteDictDataVo;
import com.feidi.xx.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提现Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinCashServiceImpl implements IFinCashService {

    private static final byte[] KEYS = "xI-XiNg*Chu^x1nG".getBytes(StandardCharsets.UTF_8);

    private final IFinConfigService finConfigService;
    private final PowCacheManager powCacheManager;
    private final FinCashMapper baseMapper;
    private final FinFlowMapper flowMapper;
    private final FinDrvWalletMapper walletMapper;
    private final FinAccountMapper finAccountMapper;
    private final FinPaymentRecordMapper paymentRecordMapper;
    private final WalletHelper walletHelper;
    private final ITransService transService;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteDriverAccountService remoteDriverAccountService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteDictService remoteDictService;
    @DubboReference
    private final RemoteImService remoteImService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteDriverConsentLogService remoteDriverConsentLogService;

    /**
     * 万能访问令牌
     */
    private static final String ANY_ACCESS_TOKEN = "$ANY@_!ACCESS!_%TOKEN#";

    /**
     * 查询提现
     *
     * @param id 主键
     * @return 提现
     */
    @Override
    public FinCashVo queryById(Long id) {
        FinCashVo vo = baseMapper.selectVoById(id);
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(vo.getDriverId());
        if (ObjectUtil.isNotNull(driverInfo)) {
            vo.setDriverTypeText(DriverTypeEnum.getInfoByCode(driverInfo.getType()));

            LambdaQueryWrapper<FinAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FinAccount::getChannel, PaymentChannelEnum.ALIPAY.getCode())
                    .eq(FinAccount::getDirection, PaymentDirectionEnum.OUTGOING.getCode())
                    .eq(FinAccount::getTenantId, driverInfo.getTenantId())
                    .orderByDesc(FinAccount::getCreateTime)
                    .last(Constants.LIMIT_ONE);
            Object payAccountConfig = null;
            FinAccount finAccount = null;
            if (DriverTypeEnum.PARTNER.getCode().equals(driverInfo.getType())) {
                log.info("司机提现详情-司机类型加盟");
                String accountConfig = RedisUtils.getCacheObject(com.feidi.xx.cross.common.cache.finance.constants.FinanceCacheConstants.FIN_CASH_PAYOUTS_KEY_PREFIX);
                if (StringUtils.isNotBlank(accountConfig)) {
                    payAccountConfig = JSONUtil.toBean(SecureUtil.aes(KEYS).decryptStr(accountConfig), Object.class);
                    queryWrapper.eq(FinAccount::getConfig, accountConfig);
                    finAccount = finAccountMapper.selectOne(queryWrapper);
                }
            } else {
                queryWrapper.eq(FinAccount::getAgentId, driverInfo.getAgentId());
                finAccount = finAccountMapper.selectOne(queryWrapper);
                if (finAccount != null) {
                    payAccountConfig = finAccount.getConfig();
                }
            }

            if (finAccount != null) {
                vo.setPayAgentName(finAccount.getAgentName());
                vo.setPayAccountTypeText(PaymentChannelEnum.getInfoByCode(finAccount.getChannel()));
            }

            if (payAccountConfig != null) {
                AliPayConfig config = JSONUtil.toBean((JSONObject) payAccountConfig, AliPayConfig.class);
                vo.setPayAppid(config.getAppId());
            }

            vo.setDriverName(driverInfo.getName());
            vo.setDriverPhone(driverInfo.getPhone());
            RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
            if (ObjectUtil.isNotNull(agentInfo)) {
                vo.setAgentName(agentInfo.getCompanyName());
            }
        }

        FinDrvWallet wallet = walletMapper.selectByDriverId(vo.getDriverId());
        if (wallet != null) {
            FinWalletVo walletVo = MapstructUtils.convert(wallet, FinWalletVo.class);
            walletVo.setTotal(NumberUtil.add(walletVo.getBalance(), walletVo.getFreeze()).longValue() + vo.getAmount());
            walletVo.setBalance(walletVo.getBalance() + vo.getAmount());
            vo.setWallet(walletVo);
        }
        return vo;
    }

    public static void main(String[] args) {
        String accountConfig = "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";

        Object payAccountConfig = JSONUtil.toBean(SecureUtil.aes(KEYS).decryptStr(accountConfig), Object.class);
        System.out.println(payAccountConfig);
    }

    /**
     * 分页查询提现列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提现分页列表
     */
    @Override
    public TableDataInfo<FinCashListVo> queryPageList(FinCashQueryBo bo, PageQuery pageQuery) {
        IPage<FinCashListVo> result = baseMapper.listByQuery(bo, pageQuery.build());
        List<FinCashListVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (FinCashListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverTypeText(DriverTypeEnum.getInfoByCode(driverInfo.getType()));
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                    }
                }
            }
        }
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的提现列表
     *
     * @param bo 查询条件
     * @return 提现列表
     */
    @Override
    public List<FinCashListVo> queryList(FinCashQueryBo bo) {
        List<FinCashListVo> records = baseMapper.listByQuery(bo);
        if (CollUtil.isNotEmpty(records)) {
            for (FinCashListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                    }
                }
            }
        }
        return records;
    }

    private Map<Long, RemoteAgentVo> getAgentMap() {
        List<RemoteAgentVo> pxAgents = remoteAgentService.getAllAgentInfo();
        return pxAgents.stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity()));
    }

    /**
     * 提现前验证
     */
    private List<FinCash> validAndGenerateFLow(FinFlow lastFlow, FinCash cash, String accessToken) {
        Long driverId = cash.getDriverId();
        // 补充信息
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(driverId);
        Assert.notNull(driverInfo, "司机不存在");

        // 万能令牌不进行校验
        if (!ANY_ACCESS_TOKEN.equals(accessToken)) {
            // 重置密码
            walletHelper.needChangePwd(driverId, false);
            // 密码校验
            walletHelper.verifyToken(driverId, PwdVerifyEnum.CASH, accessToken, false);
            // 是否有未处理提现
            walletHelper.cashApplicable(driverId, false);
        }

        // 余额是否够提现
        Assert.notNull(lastFlow, FinanceConstants.NO_FLOW_MSG);
        // 提现金额、是否可提现
        Assert.isTrue(lastFlow.getAfterCash() >= cash.getAmount(), FinanceConstants.CASH_OUT_MSG);

        if (cash.getAccountId() != null) {
            // 验证账户是否开启
            RemoteDriverAccountVo driverAccount = remoteDriverAccountService.getDriverAccount(cash.getAccountId());
            Assert.notNull(driverAccount, "提现账户不存在");
            Assert.isTrue(driverAccount.getStatus().equals(StatusEnum.ENABLE.getCode()), "账户未启用");

            // 填充基础数据
            cash.setName(driverAccount.getName());
            cash.setAccount(driverAccount.getAccount());
            cash.setAccountType(driverAccount.getType());
            cash.setPhone(LoginHelper.getUserPhone());
        }

        // 更新订单资金流向状态，需要同步处理
        List<RemoteOrderDetailVo> orders = remoteOrderService.updateOrderFlowStatus(cash.getDriverId(), OrderFlowTypeEnum.CASHING.getCode(), OrderFlowTypeEnum.NORMAL.getCode());
        // 订单总收入
        Long totalProfit = orders.stream().map(RemoteOrderDetailVo::getDriverProfit).reduce(0L, Long::sum);
        // 差值就是别人转入的钱或者其他收入
        long otherProfit = cash.getAmount() - totalProfit;

        Map<Long, List<RemoteOrderDetailVo>> agentMap = orders.stream().collect(Collectors.groupingBy(RemoteOrderDetailVo::getAgentId));
        // 至少有一个是当前代理商的，比如只有别人转的钱
        if (CollUtil.isEmpty(agentMap)) {
            agentMap.put(driverInfo.getAgentId(), new ArrayList<>());
        }

        Map<Long, RemoteAgentVo> allAgentMap = getAgentMap();

        // 几个代理商对应的几个提现单
        ArrayDeque<FinCash> cashes = new ArrayDeque<>(agentMap.size());

        for (Map.Entry<Long, List<RemoteOrderDetailVo>> entry : agentMap.entrySet()) {
            Long agentId = entry.getKey();
            // 是否当前代理商
            boolean isCurAgent = ObjectUtil.equal(driverInfo.getAgentId(), agentId);
            // 当前代理商下的收入
            Long agentProfit = entry.getValue().stream().map(RemoteOrderDetailVo::getDriverProfit).reduce(0L, Long::sum);
            // 相关订单id
            List<Long> orderIds = StreamUtils.toList(entry.getValue(), RemoteOrderDetailVo::getId);

            // 基础数据拷贝，填充子提现单数据
            FinCash tmpCash = BeanUtil.copyProperties(cash, FinCash.class);
            tmpCash.setAgentId(agentId);
            if (isCurAgent) {
                // 其他收入算在当前代理商下，也肯定是在当前代理商下
                tmpCash.setAmount(agentProfit + otherProfit);
            } else {
                tmpCash.setAmount(agentProfit);
            }
            tmpCash.setServiceFee(serviceFee(tmpCash.getAmount()));
            tmpCash.setRelatedOrder(orderIds);
            // 单号重新生成
            tmpCash.setCashNo(FinCash.generateNo());

            // 之前的代理商先生成流水
            if (isCurAgent) {
                cashes.addLast(tmpCash);
            } else {
                cashes.addFirst(tmpCash);
            }
        }
        return new ArrayList<>(cashes);
    }

    private Long serviceFee(Long amount) {
        // 计算服务费
        return 0L;
    }

    /**
     * 新增提现
     *
     * @param bo 提现
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinCashApplyBo bo) {
        FinCash add = MapstructUtils.convert(bo, FinCash.class);
        // token可能是旧的
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(add.getDriverId());
        if (driverInfo != null) {
            add.setAgentId(driverInfo.getAgentId());
            walletHelper.transferOrCashDisable(driverInfo.getId());

            if (Objects.equals(DriverTypeEnum.getByCode(driverInfo.getType()), DriverTypeEnum.SELF)) {
                throw new ServiceException(FinanceConstants.PROPRIETARY_EXPERIENCE_DISABLING_PROMPT);
            }
        }

        FinFlow lastFlow = flowMapper.getLastFlow(add.getDriverId());
        // 相关提现单
        List<FinCash> cashes = validAndGenerateFLow(lastFlow, add, bo.getAccessToken());
        for (FinCash cash : cashes) {
            boolean autoPay = checkAutoPay(cash);
            boolean cashFlag = baseMapper.insert(cash) > 0;
            Assert.isTrue(cashFlag, "提现申请失败，请重新申请");
            FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.CASH, DirectionEnum.OUT, FlowTypeEnum.CASHING, cash, driverInfo);
            boolean flowFlag = flowMapper.insert(nextFlow) > 0;
            Assert.isTrue(flowFlag, "提现申请失败，请重新申请");
            // 更新最新一条流水
            lastFlow = flowMapper.getLastFlow(add.getDriverId());

            if (autoPay) {
                AutoPaymentEvent event = new AutoPaymentEvent();
                event.setCashId(cash.getId());
                event.setTenantId(cash.getTenantId());
                scheduledExecutorService.schedule(() -> {
                    AutoPaymentProducer.sendMessage(event);
                }, 2, TimeUnit.SECONDS);
            }
        }
        boolean flag = walletMapper.updateByFlow(lastFlow) > 0;
        Assert.isTrue(flag, "钱包更新失败，请重试");
        //新增同意记录
        remoteDriverConsentLogService.insertDriverConsentLog(add.getDriverId(), ServletUtils.getClientIP());
        return flag;
    }

    private boolean checkAutoPay(FinCash cash) {
        log.info("提现自动打款开始，提现单：【{}】", JsonUtils.toJsonString(cash));
        // 自动打款开关
        String autoSwitch = finConfigService.selectConfigByKey(FinanceConstants.CASH_AUTO_SWITCH);
        log.info("提现自动打款开关：【{}】", autoSwitch);
        if (!Objects.equals(autoSwitch, IsYesEnum.YES.getCode())) {
            log.info("提现自动打款开关未开启，提现单：【{}】跳过自动打款环节", cash.getCashNo());
            return false;
        }
        // 自动打款最大金额
        String maxValue = finConfigService.selectConfigByKey(FinanceConstants.CASH_AUTO_PAY_MAX);

        if (StringUtils.isBlank(maxValue)) {
            log.info("自动打款最大金额未设置，提现单：【{}】跳过自动打款环节", cash.getCashNo());
            return false;
        }

        if (Objects.equals(maxValue, "0")) {
            log.info("自动打款最大金额：【0】，不限制自动打款金额，提现单：【{}】", cash.getCashNo());
        } else {
            Long cashAutoMax = MoneyConvertUtils.yuan2FenLong(maxValue);
            if (cash.getAmount() > cashAutoMax) {
                log.info("提现金额：【{}】超出自动打款最大金额：【{}】，提现单：【{}】跳过自动打款环节",cash.getAmount(), cashAutoMax, cash.getCashNo());
                return false;
            }
        }

        cash.setUserId(0L);
        cash.setUserName(UserTypeEnum.AUTO_USER.getInfo());
        cash.setStatus(CashAuditStatusEnum.SUCCESS.getCode());
        cash.setReviewTime(new Date());
        cash.setTransStatus(CashTransStatusEnum.TRADING.getCode());

        return true;
    }

    /**
     * 修改提现
     *
     * @param bo 提现
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean review(FinCashReviewBo bo) {
        FinCash cash = baseMapper.selectById(bo.getId());
        Assert.notNull(cash, "提现记录不存在");
        validBeforeReview(bo, cash);
        // 没问题再进行更新
        cash = MapstructUtils.convert(bo, cash);
        cash.setUserId(LoginHelper.getUserId());
        cash.setUserName(LoginHelper.getUsername());
        cash.setReviewTime(new Date());
        cash.setTransStatus(CashTransStatusEnum.TRADING.getCode());

        boolean flag = baseMapper.updateById(cash) > 0;
        if (flag) {
            CashAuditStatusEnum auditStatus = CashAuditStatusEnum.getByCode(cash.getStatus());
            switch (auditStatus) {
                case REJECT -> {
                    // 驳回 - 解冻、修改状态
                    flag = cashFail(cash);
                }
                case SUCCESS -> {
                    // 成功 - 解冻、扣余额、修改状态
                    if (cash.getIsAuto().equals(IsYesEnum.YES.getCode())) {
                        // 自动打款、直接丢到队列处理 延迟处理，消息队列可能比数据库快
                        AutoPaymentEvent event = new AutoPaymentEvent();
                        event.setCashId(cash.getId());
                        event.setTenantId(cash.getTenantId());
                        scheduledExecutorService.schedule(() -> {
                            AutoPaymentProducer.sendMessage(event);
                        }, 1, TimeUnit.SECONDS);
                    } else {
                        // 手动打款
                        flag = cashSuccess(cash);
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 审核前的数据校验
     */
    private FinCash validBeforeReview(FinCashReviewBo bo, FinCash old) {
        Assert.isTrue(CashAuditStatusEnum.ING.getCode().equals(old.getStatus()), "当前提现已处理");

        CashAuditStatusEnum auditStatus = CashAuditStatusEnum.getByCode(bo.getStatus());
        Assert.notNull(auditStatus, "请选择正确的审批状态");
        Assert.isTrue(!CashAuditStatusEnum.ING.equals(auditStatus), "请选择正确的审批状态");
        switch (auditStatus) {
            case REJECT -> {
                ValidatorUtils.validate(bo, ReviewRejectGroup.class);
            }
            case SUCCESS -> {
                ValidatorUtils.validate(bo, ReviewPassGroup.class);
                // 金额是否匹配
                Long amount = old.getAmount();
                // 默认全部
                Long actualAmount = bo.getActualAmount() == null ? old.getAmount() : bo.getActualAmount();
                if (!Objects.equals(actualAmount, amount)) {
                    throw new ServiceException("打款金额加服务费不等于提现金额");
                }
                // 非自动打款需要填写打款凭证流水号
                String isAuto = bo.getIsAuto() == null ? IsYesEnum.NO.getCode() : bo.getIsAuto();
                // 自动打款配置
                if (isAuto.equals(IsYesEnum.YES.getCode())) {
                    String open = remoteConfigService.selectValueByKey(FinanceConstants.CASH_AUTO_OPEN);
                    Assert.notNull(open, "请先配置自动打款");
                    Assert.isTrue(IsYesEnum.YES.getCode().equals(open), "自动打款开关未开启");
                    // 自动打款限额
                    long cashAutoMaxLong = 0L;
                    String cashAutoMax = remoteConfigService.selectValueByKey(FinanceConstants.CASH_AUTO_MAX);
                    if (StringUtils.isNotBlank(cashAutoMax)) {
                        cashAutoMaxLong = MoneyConvertUtils.yuan2FenLong(cashAutoMax);
                    }
                    // 0 不限额、提现金额必须小于、等于限制金额
                    Assert.isTrue(cashAutoMaxLong == 0L || (bo.getActualAmount() <= cashAutoMaxLong), "自动打款限额为" + cashAutoMax + "元");
                } else {
                    Assert.isTrue(StrUtil.isNotBlank(bo.getFlowNo()), "请填写打款流水号");
                    Assert.isTrue(StrUtil.isNotBlank(bo.getVoucher()), "请上传打款图片");
                }
            }
        }
        return MapstructUtils.convert(bo, old);
    }

    /**
     * 自动打款
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoPayment(Long id) {
        FinCash ftCash = baseMapper.selectById(id);
        Assert.notNull(ftCash, "提现单不存在");
        autoPayment(ftCash);
    }

    /**
     * 校验并批量删除提现信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 自动打款
     *
     * @param cash
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoPayment(FinCash cash) {
        log.info("自动打款开始，id：{}", cash.getId());
        RemoteDriverVo driverByDriver = remoteDriverService.getDriverByDriverId(cash.getDriverId());
        if (driverByDriver == null) {
            log.error("自动打款失败，司机不存在!");
            return;
        }
        //司机 加盟的单独打款账户
        String payAccountConfig = null;
        if (DriverTypeEnum.PARTNER.getCode().equals(driverByDriver.getType())) {
            log.info("司机组为加盟， 打款账户走redis配置的账户");
            payAccountConfig = RedisUtils.getCacheObject(com.feidi.xx.cross.common.cache.finance.constants.FinanceCacheConstants.FIN_CASH_PAYOUTS_KEY_PREFIX);
        }

        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(FinanceCacheConstants.LOCK_AUTO_PAY_KEY + cash.getId());
        try {
            boolean tryLock = lock.tryLock(FinanceCacheConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (tryLock) {
                // 金额为0的不处理
                if (cash.getAmount().equals(0L)) {
                    return;
                }

                Assert.isTrue(CashAuditStatusEnum.SUCCESS.getCode().equals(cash.getStatus()), "提现单状态无法打款");
                Assert.isTrue(CashTransStatusEnum.TRADING.getCode().equals(cash.getTransStatus()), "交易状态错误");
                // 判断该笔订单是否已经存在历史打款
                List<FinPaymentRecord> record = paymentRecordMapper.getPaymentRecord(cash.getTenantId(), cash.getId(), JoinEnum.CASH.getCode(), null, PaymentStatusEnum.SUCCESS.getCode());
                if (CollUtil.isNotEmpty(record)) {
                    List<Long> list = record.stream().map(FinPaymentRecord::getId).toList();
                    log.error("当前提现: {}已完成，请勿重复提现, 相关支付流水: {}", cash.getId(), CollUtil.join(list, ","));
                    cash.setStatus(CashAuditStatusEnum.SUCCESS.getCode());
                    cash.setRemark("当前提现已完成，请勿重复提现");
                    baseMapper.updateById(cash);
                    return;
                }
                /// TODO 构造打款参数
                TransferBo transferBo = new TransferBo();
                transferBo.setTenantId(cash.getTenantId());
                transferBo.setChannel(PaymentChannelEnum.ALIPAY);

                // APPID取租户ID
                transferBo.setAppId(cash.getTenantId());
                transferBo.setPaymentType(cash.getAccountType());
                transferBo.setOutBizNo(cash.getCashNo());
                transferBo.setName(cash.getName());
                transferBo.setAccount(cash.getAccount());
                transferBo.setTransAmount(cash.getAmount());
                transferBo.setTransTypeEnum(RecordTypeEnum.DRIVER_CASH);
                transferBo.setRemark(RecordTypeEnum.DRIVER_CASH.getInfo());
                if (StrUtil.isNotBlank(payAccountConfig)) {
                    transferBo.setPayConfig(payAccountConfig);
                }

                final TransferVo transfer;
                try {
                    String sysAgentPay = remoteConfigService.selectValueByKey(FinanceConstants.SYS_AGENT_PAY);
                    if (IsYesEnum.YES.getCode().equals(sysAgentPay)) {
                        transferBo.setAgentId(cash.getAgentId());
                    }
                    transfer = transService.transfer(transferBo);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    // 恢复审核中状态
                    cash.setStatus(CashAuditStatusEnum.ING.getCode());
                    cash.setTransStatus(CashTransStatusEnum.TRADE_FAIL.getCode());
                    cash.setRemark("发起交易失败:" + e.getMessage());
                    baseMapper.updateById(cash);
                    return;
                }

                cash.setAppId(transfer.getAppId());
                cash.setRemark(transfer.getMsg());
                /// 处理结果集
                if (transfer.getStatus().equals(SuccessFailEnum.SUCCESS)) {
                    /// TODO 成功
                    cash.setStatus(CashAuditStatusEnum.SUCCESS.getCode());
                    cash.setFlowNo(transfer.getFlowNo());
                    cash.setTransDate(DateUtil.parse(transfer.getTransDate()));
                    cash.setTransStatus(CashTransStatusEnum.TRADE_SUCCESS.getCode());
                    cash.setTransInfo(transfer.getResponseJson());
                    cash.setVoucher(transfer.getOrderId());
                    cashSuccess(cash);
                } else {
                    // 成功或者失败的都要记录信息
                    cash.setTransStatus(CashTransStatusEnum.TRADE_FAIL.getCode());
                    cash.setTransInfo(transfer.getResponseJson());
                    // 错误处理
                    if (TransferErrorEnum.getPayeeErrorList().contains(transfer.getTransferError())) {
                        // 收款账户异常的直接驳回并发短信通知司机
                        cash.setStatus(CashAuditStatusEnum.REJECT.getCode());
                        cashFail(cash);
                        cash.setRemark("提现失败，请检查提现账户信息或状态是否正确");
                        // todo 给司机发短信
                    } else {
                        //恢复成审核中状态，可以再次发起审核
                        cash.setStatus(CashAuditStatusEnum.ING.getCode());
                    }

                    try {
                        RemoteDriverVo driver = powCacheManager.getDriverInfoById(cash.getDriverId());
                        List<RemoteUserVo> finance = remoteUserService.selectUserIdsByRoleKey("cwjl");
                        List<String> financeEmails = StreamUtils.toList(finance, RemoteUserVo::getEmail);
                        ThreadUtil.execAsync(() -> sendEmail2Platform(cash, driver, transfer, financeEmails));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
                baseMapper.updateById(cash);
                // 异步发布事件
                scheduledExecutorService.submit(() -> publishEvent(cash, transferBo, transfer));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("自动提现错误，" + e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 自动打款失败，发送邮件提醒
     *
     * @param content 邮件内容
     */
    public void notifyAutoPaymentFailByEmail(String content) {
        scheduledExecutorService.schedule(() -> {
            String email = remoteUserService.selectEmailById(LoginHelper.getUserId());

            log.info("自动打款审批人邮箱：{}", email);
            if (StringUtils.isEmpty(email)) {
                log.error("自动打款审批人【{}】邮箱为空，无法发送邮件通知", LoginHelper.getUsername());
                return;
            }
            MailUtils.sendText(email, "自动打款失败提醒", content);
            log.debug("发送邮件通知财务成功：{}", email);
        }, 0, TimeUnit.SECONDS);
    }

    private void publishEvent(FinCash cash, TransferBo transferBo, TransferVo transfer) {
        PaymentRecordEvent paymentRecordEvent = new PaymentRecordEvent();
        paymentRecordEvent.setTenantId(cash.getTenantId());
        paymentRecordEvent.setJoinTable("Fin_cash");
        paymentRecordEvent.setJoinId(cash.getId());
        paymentRecordEvent.setJoinNo(transferBo.getOutBizNo());
        paymentRecordEvent.setPaymentType(transfer.getPaymentType());
        // 记录哪个代理商支付的
        paymentRecordEvent.setAppId(transfer.getAppId());
        paymentRecordEvent.setMchId(transfer.getMchId());
        paymentRecordEvent.setOutBizNo(transferBo.getOutBizNo());
        if (transfer.getTransTypeEnum() != null) {
            RecordTypeEnum recordType = transfer.getTransTypeEnum();
            paymentRecordEvent.setType(recordType.getCode());
        }
        paymentRecordEvent.setCode(transfer.getCode());
        paymentRecordEvent.setMsg(transfer.getMsg());
        paymentRecordEvent.setFlowNo(transfer.getFlowNo());
        paymentRecordEvent.setOrderId(transfer.getOrderId());
        paymentRecordEvent.setAmount(transfer.getAmount());
        paymentRecordEvent.setStatus(transfer.getStatus().getCode());
        paymentRecordEvent.setDirection(DirectionEnum.OUT.getCode());
        paymentRecordEvent.setParamsJson(transfer.getParamsJson());
        paymentRecordEvent.setResponseJson(transfer.getResponseJson());
        paymentRecordEvent.setTradeTime(transfer.getTransDate());
        PaymentRecordProducer.sendMessage(paymentRecordEvent);
    }

    private void sendEmail2Platform(FinCash cash, RemoteDriverVo driver, TransferVo transfer, List<String> emails) {
        String title = "城际顺风车《喜行出行》系统：处理司机提现异常";
        String emailTemplate = """
                <p>各位同学好！</p>
                <p>我们在处理提现单时遇到了问题，具体情况如下：</p>
                <ul>
                    <li>处理人: {}</li>
                    <li>处理时间: {}</li>
                    <li>提现单编号：{}</li>
                    <li>提现金额：{}元</li>
                    <li>提现账户名：{}</li>
                    <li>提现账户：{}</li>
                    <li>司机ID：{}</li>
                    <li>司机姓名：{}</li>
                    <li>司机手机：{}</li>
                </ul>
                <ul>
                    <li>失败原因：{}</li>
                    <li>具体错误信息：{}</li>
                </ul>
                <p>请大家注意查看，如有疑问，请随时联系研发同学，谢谢。</p>
                <p>技术研发部  {}</p>
                <p><br></p>
                <p>---------------------------------------原始错误报文---------------------------------------- </p>
                <p>请求参数：{}</p>
                <p>返回报文：{}</p>
                """;
        String content = StrUtil.format(
                emailTemplate,
                cash.getUserName(),
                DateUtil.formatDateTime(cash.getReviewTime()),
                cash.getCashNo(),
                MoneyConvertUtils.fen2YuanStr(cash.getAmount()),
                cash.getName(),
                cash.getAccount(),
                cash.getDriverId(),
                driver != null ? driver.getName() : null,
                cash.getPhone(),
                transfer.getSubMsg(),
                transfer.getCode() + ": " + transfer.getMsg() + "，" + transfer.getSubCode() + ": " + transfer.getSubMsg(),
                DateUtil.formatDateTime(new Date()),
                transfer.getParamsJson(),
                transfer.getResponseJson()
        );
        if (CollUtil.isEmpty(emails)) {
            log.error("相关通知邮箱为空，内容：{}", content);
        } else {
            MailUtils.sendHtml(emails, title, content);
        }
    }


    /**
     * 成功 - 更改提现状态和流水状态
     *
     * @param update
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public Boolean cashSuccess(FinCash update) {
        String tableName = TableInfoHelper.getTableInfo(FinCash.class).getTableName();
        FinFlow flowOut = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.OUT, FlowTypeEnum.CASHING.getCode());
        Assert.notNull(flowOut, "系统错误，未找到对应的流水，请联系管理员");
        flowOut.setType(FlowTypeEnum.CASH_SUB.getCode());
        // 取备注内容
        flowOut.setRemark(update.getRemark());
        boolean flag = flowMapper.updateById(flowOut) > 0;
        if (flag) {
            FinFlow lastFlow = flowMapper.getLastFlow(update.getDriverId());
            // 应该用最新的流水去更新钱包
            flag = walletMapper.updateByFlowAndAddExpend(lastFlow, flowOut.getCash()) > 0;
            // 更新订单资金流向状态，需要同步处理
            try {
                remoteOrderService.updateOrderFlowStatus(update.getDriverId(), OrderFlowTypeEnum.CASHED.getCode(), OrderFlowTypeEnum.CASHING.getCode());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("提现完成订单资金流向状态修改失败，{}， {}", JSONUtil.toJsonStr(update), e.getMessage());
            }
            // 推送消息
            scheduledExecutorService.submit(() -> pushMessage(update, PushTypeEnum.WITHDRAWAL_SUCCESS));
        }
        return flag;
    }

    /**
     * 失败 - 解冻
     *
     * @param update
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public Boolean cashFail(FinCash update) {
        String tableName = TableInfoHelper.getTableInfo(FinCash.class).getTableName();
        FinFlow flowOut = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.IN, FlowTypeEnum.CASH_ADD.getCode());
        Assert.isNull(flowOut, "系统错误，该笔提现已退回");
        // 原来的提现记录，需要更新状态为失败
        FinFlow cashing = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.OUT, FlowTypeEnum.CASHING.getCode());
        cashing.setType(FlowTypeEnum.CASH_FAIL.getCode());
        boolean flag = flowMapper.updateById(cashing) > 0;
        if (flag) {
            RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(update.getDriverId());
            FinFlow lastFlow = flowMapper.getLastFlow(update.getDriverId());
            FinFlow flowIn = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.CASH, DirectionEnum.IN, FlowTypeEnum.CASH_ADD, update, driverInfo);
            // 驳回的备注
            flowIn.setRemark(update.getRemark());
            flowIn.setTenantId(update.getTenantId());
            flag = flowMapper.insert(flowIn) > 0;
            if (flag) {
                flag = walletMapper.updateByFlow(flowIn) > 0;
                // 更新订单资金流向状态，需要同步处理
                try {
                    remoteOrderService.updateOrderFlowStatus(update.getDriverId(), OrderFlowTypeEnum.NORMAL.getCode(), OrderFlowTypeEnum.CASHING.getCode());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    log.error("提现失败订单资金流向状态修改失败，{}， {}", JSONUtil.toJsonStr(update), e.getMessage());
                }
                // 推送消息
                scheduledExecutorService.submit(() -> pushMessage(update, PushTypeEnum.WITHDRAWAL_FAILURE));
            }
        }
        return flag;
    }

    /**
     * 批量提现
     *
     * @param agentIds
     */
    @Override
    public void batchWithdraw(List<Long> agentIds) {
        RemoteDriverQueryBo bo = new RemoteDriverQueryBo();
        bo.setAgentIds(agentIds);
        List<RemoteDriverVo> driverVos = remoteDriverService.queryDriverInfo(bo);
        Map<Long, RemoteDriverVo> driverVoMap = StreamUtils.toMap(driverVos, RemoteDriverVo::getId, Function.identity());
        List<Long> driverIds = StreamUtils.toList(driverVos, RemoteDriverVo::getId);
        if (CollUtil.isNotEmpty(driverVos)) {
            List<FinDrvWallet> wallets = walletMapper.listByDriverId(driverIds);
            List<FinDrvWallet> drvWallets = wallets.parallelStream().filter(e -> e.getBalance() > 0).toList();
            if (CollUtil.isNotEmpty(drvWallets)) {
                List<RemoteDriverAccountVo> accounts = remoteDriverAccountService.getDriverAccounts(driverIds);
                Map<Long, List<RemoteDriverAccountVo>> accountMap = StreamUtils.groupByKey(accounts, RemoteDriverAccountVo::getDriverId);
                for (FinDrvWallet drvWallet : drvWallets) {
                    FinCashApplyBo applyBo = new FinCashApplyBo();
                    RemoteDriverVo driverVo = driverVoMap.get(drvWallet.getDriverId());
                    try {
                        // 全部提现
                        applyBo.setTenantId(drvWallet.getTenantId());
                        applyBo.setAmount(drvWallet.getBalance());
                        applyBo.setAgentId(driverVo.getAgentId());
                        applyBo.setDriverId(driverVo.getId());
                        // 万能令牌
                        applyBo.setAccessToken(ANY_ACCESS_TOKEN);
                        // 账号不存在也可以提现
                        List<RemoteDriverAccountVo> alipay = accountMap.getOrDefault(driverVo.getId(), new ArrayList<>())
                                .stream().filter(e -> e.getType().equals(AccountTypeEnum.ALIPAY.getCode())).toList();
                        if (CollUtil.isNotEmpty(alipay)) {
                            RemoteDriverAccountVo driverAccountVo = alipay.get(0);
                            applyBo.setAccountId(driverAccountVo.getId());
                        }
                        log.info("开始处理司机【{}】提现：{}", driverVo.getName(), JSONUtil.toJsonStr(applyBo));
                        insertByBo(applyBo);
                        log.info("结束处理司机【{}】提现", driverVo.getName());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        log.error("处理司机【{}】提现异常：{}", driverVo.getName(), JSONUtil.toJsonStr(applyBo));
                    }
                }

            }
        }

    }

    private void pushMessage(FinCash cash, PushTypeEnum pushType) {
        try {
            PushEvent event = new PushEvent(pushType);

            event.setTenantId(cash.getTenantId());

            // 接收人
            event.getReceiverParam().setUserType(UserTypeEnum.DRIVER_USER.getUserType());
            event.getReceiverParam().setUserId(cash.getDriverId());

            // 语音
            if (pushType.getVoice() != null) {
                RemoteDictDataVo dataVo = remoteDictService.selectDictDataByTypeAndLabel(VoiceConstant.SYSTEM_VOICE_BROADCAST, pushType.getVoice());
                if (dataVo != null) {
                    PushEvent.GtParam gtParam = event.getGtParam();
                    gtParam.setPayload(dataVo.getDictValue());
                    event.setGtParam(gtParam);
                }
            }

            List<String> cid = remoteImService.getCid(UserTypeEnum.DRIVER_USER.getUserType(), cash.getDriverId());
            event.setCids(cid);
            PushMsgProducer.sendMessage(event);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("提现消息推送异常");
        }
    }
}
