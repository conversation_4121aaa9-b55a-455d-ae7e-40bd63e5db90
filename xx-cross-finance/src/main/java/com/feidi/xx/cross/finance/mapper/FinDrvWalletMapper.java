package com.feidi.xx.cross.finance.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.bo.FinWalletQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletListVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.executor.BatchResult;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 司机钱包Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface FinDrvWalletMapper extends BaseMapperPlus<FinDrvWallet, FinDrvWalletVo> {

    default List<FinDrvWallet> listByDriverId(Collection<Long> driverIds) {
        return selectList(Wrappers.<FinDrvWallet>lambdaQuery()
                .in(FinDrvWallet::getDriverId, driverIds));
    }

    @Override
    default int updateById(FinDrvWallet entity) {
        throw new ServiceException("禁止直接更新钱包，请通过updateByFlow方法进行更新");
    }

    @Override
    default List<BatchResult> updateById(Collection<FinDrvWallet> entityList) {
        throw new ServiceException("禁止直接更新钱包，请通过updateByFlow方法进行更新");
    }

    @Override
    default List<BatchResult> updateById(Collection<FinDrvWallet> entityList, int batchSize) {
        throw new ServiceException("禁止直接更新钱包，请通过updateByFlow方法进行更新");
    }

    default FinDrvWallet selectByDriverId(Long driverId) {
        return selectOne(Wrappers.<FinDrvWallet>lambdaQuery().eq(FinDrvWallet::getDriverId, driverId));
    }

    /**
     * 根据最新流水更新余额和冻结金额
     * @param flow 最新的流水
     * @return
     */
    default int updateByFlow(FinFlow flow) {
        return update(Wrappers.<FinDrvWallet>lambdaUpdate()
                .eq(FinDrvWallet::getDriverId, flow.getDriverId())
                .set(FinDrvWallet::getBalance, flow.getAfterCash())
                .set(FinDrvWallet::getFreeze, flow.getAfterAmount() - flow.getAfterCash())
                .set(FinDrvWallet::getUpdateBy, LoginHelper.getUserId())
                .set(FinDrvWallet::getUpdateTime, new Date())
        );
    }

    /**
     * 根据流水更新并追加出账金额
     * @param flow 最新的流水
     * @param expend 追加的出账金额，仅转出和提现成功会增加
     * @return
     */
    default int updateByFlowAndAddExpend(FinFlow flow, Long expend) {
        // todo 改为查询提现列表和转账列表获取
        return update(Wrappers.<FinDrvWallet>lambdaUpdate()
                .eq(FinDrvWallet::getDriverId, flow.getDriverId())
                .set(FinDrvWallet::getBalance, flow.getAfterCash())
                .set(FinDrvWallet::getFreeze, flow.getAfterAmount() - flow.getAfterCash())
                .setSql("expend = expend + {0}", expend)
                .set(FinDrvWallet::getUpdateBy, LoginHelper.getUserId())
                .set(FinDrvWallet::getUpdateTime, new Date())
        );
    }

    Page<FinWalletListVo> listByBo(@Param("bo") FinWalletQueryBo bo, Page<FinDrvWallet> page);

    Page<FinWalletListVo> listByTime(@Param("bo") FinWalletQueryBo bo, Page<FinDrvWallet> page);

    List<FinWalletListVo> listByBo(@Param("bo") FinWalletQueryBo bo);

    /**
     * 更新钱包状态
     */
    default int updateByBo(FinDrvWallet entity) {
        return update(Wrappers.<FinDrvWallet>lambdaUpdate()
                .eq(FinDrvWallet::getDriverId, entity.getDriverId())
                .set(FinDrvWallet::getStatus,entity.getStatus())
                .set(FinDrvWallet::getUpdateTime, new Date())
        );
    }
}
