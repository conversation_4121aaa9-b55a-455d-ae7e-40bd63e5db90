package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.core.ExcelResult;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.finance.ApprovalStatusEnum;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsImportBo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestsExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestsVo;
import com.feidi.xx.cross.finance.service.IFinApprovalRequestsService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 后台 - 账单审批记录
 * 前端访问路由地址为:/finance/approvalRequests
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/approvalRequests")
public class FinApprovalRequestsController extends BaseController {

    private final IFinApprovalRequestsService finApprovalRequestsService;

    /**
     * 查询账单审批记录列表
     */
    @SaCheckPermission("finance:approvalRequests:list")
    @GetMapping("/list")
    public TableDataInfo<FinApprovalRequestsVo> list(FinApprovalRequestsBo bo, PageQuery pageQuery) {
        return finApprovalRequestsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出账单审批记录列表
     */
    @SaCheckPermission("finance:approvalRequests:export")
    @Log(title = "账单审批记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Download(name = "账单审批记录", module = ModuleConstants.FINANCE, mode = "no")
    public Object export(@RequestBody FinApprovalRequestsBo bo, HttpServletResponse response) {
        List<FinApprovalRequestsVo> list = finApprovalRequestsService.queryList(bo);
        var exportVos = list.stream()
                .flatMap(farv -> farv.getDetails().stream()
                        .flatMap(detail -> detail.getRelateOrders().stream()
                                .map(relateOrder -> FinApprovalRequestsExportVo.create(farv, detail, relateOrder)))).toList();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "账单审批记录", FinApprovalRequestsExportVo.class, outputStream);
        return outputStream.toByteArray();
    }


    /**
     * 获取账单审批记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:approvalRequests:query")
    @GetMapping("/{id}")
    public R<FinApprovalRequestsVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(finApprovalRequestsService.queryById(id));
    }

    /**
     * 新增账单审批记录
     */
    @SaCheckPermission("finance:approvalRequests:add")
    @Log(title = "账单审批记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinApprovalRequestsBo bo) {
        return toAjax(finApprovalRequestsService.insertByBo(bo));
    }

    /**
     * 修改账单审批记录
     */
    @SaCheckPermission("finance:approvalRequests:edit")
    @Log(title = "账单审批记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinApprovalRequestsBo bo) {
        return toAjax(finApprovalRequestsService.updateByBo(bo));
    }

    /**
     * 删除账单审批记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:approvalRequests:remove")
    @Log(title = "账单审批记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finApprovalRequestsService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 状态操作
     *
     * @param bo
     * @return
     */
    @SaCheckPermission(value = {"finance:approvalRequests:approve", "finance:approvalRequests:revoke"}, mode = SaMode.OR)
    @Log(title = "账单审批状态操作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("updateStatus")
    public R<Void> updateStatus(@RequestBody FinApprovalRequestsBo bo) {
        Assert.notNull(bo.getId(), "主键不能为空");
        Assert.notNull(bo.getStatus(), "状态不能为空");
        ApprovalStatusEnum.fromCode(bo.getStatus());

        finApprovalRequestsService.updateStatus(bo);
        return R.ok();
    }


    /**
     * 导入数据解析
     *
     * @param file
     * @return
     * @throws IOException
     */
    @SaCheckPermission("finance:approvalRequests:importDataParser")
    @Log(title = "账单审批导入", businessType = BusinessType.IMPORT)
    @RepeatSubmit()
    @PostMapping(value = "importDataParser", consumes = "multipart/form-data")
    public R<FinApprovalRequestsVo> importDataParser(@NotNull @RequestParam("file") MultipartFile file) throws IOException {
        ExcelResult<FinApprovalRequestsImportBo> excelResult = ExcelUtil.importExcel(file.getInputStream(), FinApprovalRequestsImportBo.class, true);
        if (CollUtil.isEmpty(excelResult.getList())) {
            return R.fail("导入数据为空");
        }
        log.debug("导入的数据 {}", excelResult.getList());
        FinApprovalRequestsVo res = finApprovalRequestsService.importDataProcess(excelResult.getList());
        return R.ok(res);
    }
}
