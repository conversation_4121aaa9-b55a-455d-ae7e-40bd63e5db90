package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.finance.FinanceConstants;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletBo;
import com.feidi.xx.cross.finance.domain.bo.FinWalletQueryBo;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletListVo;
import com.feidi.xx.cross.finance.mapper.FinCashMapper;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import com.feidi.xx.cross.finance.service.helper.WalletHelper;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 司机钱包Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@RequiredArgsConstructor
@Service
public class FinDrvWalletServiceImpl implements IFinDrvWalletService {

    private static final Logger log = LoggerFactory.getLogger(FinDrvWalletServiceImpl.class);
    private final FinDrvWalletMapper baseMapper;

    private final FinFlowMapper flowMapper;

    private final WalletHelper walletHelper;

    private final FinCashMapper cashMapper;

    private final PowCacheManager powCacheManager;

    @DubboReference
    private final RemoteDriverService remoteDriverService;

    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 获取司机钱包
     * @param driverId
     * @return
     */
    @Override
    public FinDrvWalletVo queryByDriverId(Long driverId) {
        FinDrvWallet wallet = baseMapper.selectByDriverId(driverId);
        FinDrvWalletVo vo = MapstructUtils.convert(wallet, FinDrvWalletVo.class);
        vo.setTotal(NumberUtil.add(vo.getBalance(), vo.getFreeze()).longValue());
        try {
            CompletableFuture<Boolean> changePwd = CompletableFuture.supplyAsync(() -> walletHelper.needChangePwd(driverId));
            CompletableFuture<Boolean> init = CompletableFuture.supplyAsync(() -> walletHelper.initAccount(driverId));
            CompletableFuture<String> version = CompletableFuture.supplyAsync(() -> remoteConfigService.selectValueByKey(FinanceConstants.SYSTEM_VERSION_RELEASE));
            // 异步获取
            CompletableFuture<Void> allOf = CompletableFuture.allOf(changePwd, init, version);
            allOf.get();
            // 获取数据
            vo.setChangePwd(changePwd.get());
            vo.setInitAccount(init.get());
            String ver = version.get();
            Assert.notNull(ver, "请配置【版本发布状态【Y.发布中|N.未发布】】");
            vo.setVersion(ver);
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("获取钱包信息失败");
        }
        return vo;
    }

    /**
     * 查询司机钱包
     *
     * @param id 主键
     * @return 司机钱包
     */
    @Override
    public FinDrvWalletVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机钱包分页列表
     */
    @Override
    public TableDataInfo<FinWalletListVo> queryPageList(FinWalletQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNotNull(bo.getAgentId())) {
            bo.setAgentIds(Arrays.asList(bo.getAgentId()));
        }
        List<Long> driverIds = getDriverIds(bo.getAgentIds());
        bo.setDriverIds(driverIds);
        if (CollUtil.isEmpty(driverIds)) {
            return TableDataInfo.build();
        }
        Page<FinWalletListVo> result = null;
        if (StringUtils.isNotBlank(bo.getStartCreateTime()) && StringUtils.isNotBlank(bo.getEndCreateTime())) {
            result = baseMapper.listByTime(bo, pageQuery.build());
        } else {
            result = baseMapper.listByBo(bo, pageQuery.build());
        }
        List<FinWalletListVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (FinWalletListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    record.setAgentId(driverInfo.getAgentId());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                    }
                }
            }
        }
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的司机钱包列表
     *
     * @param bo 查询条件
     * @return 司机钱包列表
     */
    @Override
    public List<FinWalletListVo> queryList(FinWalletQueryBo bo) {
        if (ObjectUtil.isNotNull(bo.getAgentId())) {
            bo.setAgentIds(Arrays.asList(bo.getAgentId()));
        }
        List<Long> driverIds = getDriverIds(bo.getAgentIds());
        bo.setDriverIds(driverIds);
        if (CollUtil.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        List<FinWalletListVo> records = baseMapper.listByBo(bo);
        if (CollUtil.isNotEmpty(records)) {
            for (FinWalletListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                        SysDistrictCacheVo cacheVo = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(agentInfo.getCityId()));
                        if (ObjectUtil.isNotNull(cacheVo)) {
                            record.setCity(cacheVo.getName());
                        }
                    }
                }
            }
        }
        return records;
    }

    private List<Long> getDriverIds(List<Long> agentIds) {
        if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
            if (CollUtil.isEmpty(agentIds)) {
                return Collections.emptyList();
            }
        }

        RemoteDriverQueryBo driverQueryBo = new RemoteDriverQueryBo();
        driverQueryBo.setAgentIds(agentIds);
        driverQueryBo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        List<RemoteDriverVo> driverVos = remoteDriverService.queryDriverInfo(driverQueryBo);
        return StreamUtils.toList(driverVos, RemoteDriverVo::getId);
    }

    /**
     * 修改司机钱包
     *
     * @param bo 司机钱包
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(FinDrvWalletBo bo) {
        FinDrvWallet wallet = baseMapper.selectById(bo.getId());
        Assert.notNull(wallet, "司机钱包不存在");
        Long driverId = wallet.getDriverId();
        Long adjustAmount = bo.getAdjustAmount();

        FinFlow lastFlow = flowMapper.getLastFlow(driverId);
        DirectionEnum direction = DirectionEnum.getByCode(bo.getDirection());
        if (DirectionEnum.OUT.equals(direction)) {
            Assert.notNull(lastFlow, "司机资金不足，无法转出");
            Assert.isTrue(ArithUtils.sub(lastFlow.getAfterAmount(), adjustAmount) >= 0, "账户余额不足");
        }

        RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(driverId);
        FlowTypeEnum type = DirectionEnum.IN.equals(direction)? FlowTypeEnum.ADMIN_ADD: FlowTypeEnum.ADMIN_SUB;
        FinFlow flow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.MANUAL, direction, type, wallet, driverInfo, adjustAmount);
        flow.setRemark(bo.getRemark());
        boolean ret = flowMapper.insert(flow) > 0;
        if (ret) {
            ret = baseMapper.updateByFlow(flow) > 0;
        }
        return ret;
    }
    /**
     * 司机钱包禁用/启用
     *
     * @param driverId 司机id
     * @param status 钱包状态
     * @return 是否禁用/启用成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean disableWallet(Long driverId, String status) {
        //钱包禁用
        FinDrvWallet FinDrvWallet = baseMapper.selectByDriverId(driverId);
        if (!FinDrvWallet.getStatus().equals(status) && StatusEnum.getByCode(status) != null) {
            FinDrvWallet.setStatus(status);
            //更新钱包status
            boolean ret = baseMapper.updateByBo(FinDrvWallet) > 0;
            //司机账号同时停用
            if (ret && StatusEnum.DISABLE.getCode().equals(status)) {
                RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(driverId);
                //判断司机状态若为正常则禁用
                if (driverInfo.getStatus().equals(UserStatusEnum.OK.getCode())) {
                    remoteDriverService.updateStatus(driverId, status);
                }
                //钱包禁用后提现中的提现单禁止提现
                List<FinCash> FinCashes = cashMapper.listByStatus(driverId, CashAuditStatusEnum.ING);
                //将申请中状态更改为驳回
                for (FinCash FinCash : FinCashes) {
                    FinCash.setStatus(CashAuditStatusEnum.REJECT.getCode());
                    cashMapper.updateById(FinCash);
                }
            }
            return ret;
        }
        return true;
    }

    /**
     * 根据司机id查询司机钱包
     *
     * @param driverIds 司机id
     * @return 司机钱包
     */
    @Override
    public List<FinDrvWallet> queryByDriverIds(List<Long> driverIds) {
        if (CollUtil.isEmpty(driverIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FinDrvWallet> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(FinDrvWallet::getDriverId, driverIds);

        return baseMapper.selectList(lambdaQuery);
    }
}
