<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper">
    <select id="listByBo" resultType="com.feidi.xx.cross.finance.domain.vo.FinWalletListVo">
        with flow as (
        select
        driver_id, tenant_id,
        ifnull(sum(if(type in('22', '24'), amount, NULL)), 0) as orderAmount,
        ifnull(sum(if(type = '23', amount, NULL)), 0) as orderComplaint,
        ifnull(sum(if(type = '51', amount, NULL)), 0) as rewardAmount,
        ifnull(sum(if(type = '52', amount, NULL)), 0) as rewardComplaint,
        ifnull(sum(if(type in('62', '64'), amount, NULL)), 0) as resellAmount,
        ifnull(sum(if(type = '63', amount, NULL)), 0) as resellComplaint,
        ifnull(sum(if(type = '101', amount, NULL)), 0) as adjustAmount,
        ifnull(sum(if(type = '41', amount, NULL)), 0) as cashSuccess,
        ifnull(sum(if(type = '43', amount, NULL)), 0) as cashing
        from fin_flow
        where amount != 0
        group by driver_id, tenant_id
        )
        select w.id,
        w.driver_id, w.update_time,w.status,
        w.balance, w.freeze, w.balance + w.freeze as total,
        ifnull(f.orderAmount + f.rewardAmount + f.resellAmount - f.orderComplaint - f.rewardComplaint - f.resellComplaint, 0) as reduce_profit,
        ifnull(f.orderAmount - f.orderComplaint, 0) as orderAmount,
        ifnull(f.rewardAmount - f.rewardComplaint, 0) as rewardAmount,
        ifnull(f.resellAmount - f.resellComplaint, 0) as resellAmount,
        ifnull(f.adjustAmount, 0) as adjustAmount,
        ifnull(f.cashSuccess, 0) as reduceCash,
        ifnull(f.cashing, 0) as cashing
        from fin_drv_wallet w
        left join flow f on w.driver_id = f.driver_id
        <where>
            w.del_flag = 0
            <if test="bo.driverId != null and bo.driverId != ''">
                and w.driver_id = #{bo.driverId}
            </if>
            <if test="bo.driverIds != null and bo.driverIds.size() > 0">
                AND w.driver_id in
                <foreach collection="bo.driverIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.startUpdateTime != null and bo.startUpdateTime != '' and bo.endUpdateTime != null and bo.endUpdateTime != ''">
                AND w.update_time between #{bo.startUpdateTime} and #{bo.endUpdateTime}
            </if>
        </where>
        order by w.update_time desc
    </select>

    <select id="listByTime" resultType="com.feidi.xx.cross.finance.domain.vo.FinWalletListVo">
        with flow as (
        select
        driver_id, tenant_id,
        ifnull(sum(if(type in('22'), amount, NULL)), 0) as balance,
        ifnull(sum(if(type in('21'), amount, NULL)), 0) as freeze,
        ifnull(sum(if(type in('22', '24'), amount, NULL)), 0) as orderAmount,
        ifnull(sum(if(type = '23', amount, NULL)), 0) as orderComplaint,
        ifnull(sum(if(type = '51', amount, NULL)), 0) as rewardAmount,
        ifnull(sum(if(type = '52', amount, NULL)), 0) as rewardComplaint,
        ifnull(sum(if(type in('62', '64'), amount, NULL)), 0) as resellAmount,
        ifnull(sum(if(type = '63', amount, NULL)), 0) as resellComplaint,
        ifnull(sum(if(type = '101', amount, NULL)), 0) as adjustAmount,
        ifnull(sum(if(type = '41', amount, NULL)), 0) as cashSuccess,
        ifnull(sum(if(type = '43', amount, NULL)), 0) as cashing
        from (
            SELECT driver_id, tenant_id, create_time, type, amount
            FROM fin_flow
            WHERE create_time between #{bo.startCreateTime} and #{bo.endCreateTime}
            AND amount != 0
        ) AS subquery
        group by driver_id, tenant_id
        )
        select w.id,
        w.driver_id, w.update_time,w.status,
        f.balance, f.freeze, f.balance + f.freeze as total,
        ifnull(f.orderAmount + f.rewardAmount + f.resellAmount - f.orderComplaint - f.rewardComplaint - f.resellComplaint, 0) as reduce_profit,
        ifnull(f.orderAmount - f.orderComplaint, 0) as orderAmount,
        ifnull(f.rewardAmount - f.rewardComplaint, 0) as rewardAmount,
        ifnull(f.resellAmount - f.resellComplaint, 0) as resellAmount,
        ifnull(f.adjustAmount, 0) as adjustAmount,
        ifnull(f.cashSuccess, 0) as reduceCash,
        ifnull(f.cashing, 0) as cashing
        from fin_drv_wallet w
        left join flow f on w.driver_id = f.driver_id
        <where>
            w.del_flag = 0
            <if test="bo.driverId != null and bo.driverId != ''">
                and w.driver_id = #{bo.driverId}
            </if>
            <if test="bo.driverIds != null and bo.driverIds.size() > 0">
                AND w.driver_id in
                <foreach collection="bo.driverIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.startUpdateTime != null and bo.startUpdateTime != '' and bo.endUpdateTime != null and bo.endUpdateTime != ''">
                AND w.update_time between #{bo.startUpdateTime} and #{bo.endUpdateTime}
            </if>
        </where>
        order by w.update_time desc
    </select>
</mapper>
