package com.feidi.xx.cross.member.dubbo;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.member.mapper.MbrPassengerMapper;
import com.feidi.xx.cross.member.service.IMbrPassengerService;
import com.feidi.xx.cross.member.service.IMbrVoucherService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerLoginVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 乘客信息远程调用
 *
 * <AUTHOR>
 * @create 2024-08-07-14:12
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemotePassengerServiceImpl implements RemotePassengerService {

    /// 乘客服务
    final MbrPassengerMapper baseMapper;
    final IMbrVoucherService voucherService;
    final IMbrPassengerService passengerService;

    /**
     * 乘客登录
     *
     * @param remotePassengerLoginVo
     * @return
     */
    @Override
    public XcxLoginUser login(RemotePassengerLoginVo remotePassengerLoginVo) {
        return passengerService.login(remotePassengerLoginVo);
    }

    @Override
    public String getOpenId(Long passengerId) {
        return voucherService.getOpenId(passengerId);
    }

    @Override
    public RemotePassengerVo getPassengerInfo(Long id) {
        MbrPassenger passenger = baseMapper.selectById(id);
        if (passenger != null) {
            RemotePassengerVo passengerVo = BeanUtils.copyProperties(passenger, RemotePassengerVo.class);
            String openId = voucherService.getOpenId(id);
            passengerVo.setOpenId(openId);
            return passengerVo;
        }
        return null;
    }

    @Override
    public List<RemotePassengerVo> getPassengerInfo(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            // 返回空列表，避免执行无效 SQL
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MbrPassenger> lqw = new LambdaQueryWrapper<>();
        lqw.in(MbrPassenger::getId, ids);
        List<MbrPassengerVo> mbrPassengerVos = baseMapper.selectVoList(lqw);
        return BeanUtils.copyToList(mbrPassengerVos, RemotePassengerVo.class);
    }

    /**
     * 司机注销
     *
     * @param id
     * @return
     */
    @Override
    public boolean signOut(Long id) {
        LambdaUpdateWrapper<MbrPassenger> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MbrPassenger::getStatus, UserStatusEnum.DELETED.getCode())
                .set(MbrPassenger::getUpdateTime, DateUtils.getNowDate())
                .eq(MbrPassenger::getId, id);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    public RemotePassengerVo getPassengerInfo(RemotePassengerLoginVo remotePassengerLoginVo) {
        return passengerService.getPassengerInfo(remotePassengerLoginVo);
    }

    @Override
    public Integer getCountByDriverId(Long driverId) {
        if (driverId != null) {
            LambdaQueryWrapper<MbrPassenger> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(MbrPassenger::getDriverId, driverId);
            queryWrapper.eq(MbrPassenger::getStatus, StatusEnum.ENABLE.getCode());
            return Math.toIntExact(baseMapper.selectCount(queryWrapper));
        }
        return 0;
    }

    @Override
    public void bindPassenger(String userType, Long inviteAgentId, Long inviteDriverId, Long passengerId) {
        if (UserTypeEnum.AGENT_USER.getUserType().equals(userType) || UserTypeEnum.DRIVER_USER.getUserType().equals(userType)) {
            if (inviteAgentId != null && passengerId != null) {
                LambdaUpdateWrapper<MbrPassenger> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(MbrPassenger::getAgentId, inviteAgentId)
                        .set(ObjectUtils.isNotNull(inviteDriverId), MbrPassenger::getDriverId, inviteDriverId)
                        .eq(MbrPassenger::getId, passengerId);
                baseMapper.update(updateWrapper);
            }
        }
    }

    /**
     * 根据手机尾号查询乘客
     *
     * @param phoneEnd 手机尾号
     * @return 乘客信息集合
     */
    @Override
    public List<RemotePassengerVo> queryByPhoneEnd(String phoneEnd) {
        LambdaQueryWrapper<MbrPassenger> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(MbrPassenger::getPhone, phoneEnd);
        queryWrapper.eq(MbrPassenger::getStatus, StatusEnum.ENABLE.getCode());
        List<MbrPassenger> mbrPassengers = baseMapper.selectList(queryWrapper);
        return BeanUtils.copyToList(mbrPassengers, RemotePassengerVo.class);
    }

    @Override
    public RemotePassengerVo queryByPhone(String phone) {
        LambdaQueryWrapper<MbrPassenger> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MbrPassenger::getPhone, phone).
                eq(MbrPassenger::getStatus, StatusEnum.ENABLE.getCode())
                .orderByDesc(MbrPassenger::getCreateTime)
                .last("limit 1");
        return BeanUtils.copyProperties(baseMapper.selectOne(queryWrapper), RemotePassengerVo.class);
    }

    /**
     * 根据创建时间查询
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 乘客信息集合
     */
    @Override
    public List<RemotePassengerVo> getByCreateTime(String startTime, String endTime) {
        LambdaQueryWrapper<MbrPassenger> lqw = new LambdaQueryWrapper<>();
        lqw.between(MbrPassenger::getCreateTime, startTime, endTime);
        List<MbrPassenger> mbrPassengerVos = baseMapper.selectList(lqw);
        return BeanUtils.copyToList(mbrPassengerVos, RemotePassengerVo.class);
    }

    @Override
    public void bindInviter(Long passengerId, Long inviterId) {
        if (passengerId == null || inviterId == null) {
            return;
        }
        LambdaUpdateWrapper<MbrPassenger> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MbrPassenger::getParentId, inviterId)
                .eq(MbrPassenger::getId, passengerId);
        baseMapper.update(updateWrapper);
    }

    @Override
    public RemotePassengerVo register(RemotePassengerLoginVo remotePassengerLoginVo) {
        MbrPassenger passenger = passengerService.register(remotePassengerLoginVo);
        return BeanUtils.copyProperties(passenger, RemotePassengerVo.class);
    }
}
