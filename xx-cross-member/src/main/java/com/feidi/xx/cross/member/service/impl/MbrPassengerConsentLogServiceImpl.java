package com.feidi.xx.cross.member.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.member.domain.MbrPassengerConsentLog;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerConsentLogBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerConsentLogVo;
import com.feidi.xx.cross.member.mapper.MbrPassengerConsentLogMapper;
import com.feidi.xx.cross.member.service.IMbrPassengerConsentLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 乘客协议同意记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RequiredArgsConstructor
@Service
public class MbrPassengerConsentLogServiceImpl implements IMbrPassengerConsentLogService {

    private final MbrPassengerConsentLogMapper baseMapper;

    /**
     * 查询乘客协议同意记录
     *
     * @param id 主键
     * @return 乘客协议同意记录
     */
    @Override
    public MbrPassengerConsentLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客协议同意记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客协议同意记录分页列表
     */
    @Override
    public TableDataInfo<MbrPassengerConsentLogVo> queryPageList(MbrPassengerConsentLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MbrPassengerConsentLog> lqw = buildQueryWrapper(bo);
        Page<MbrPassengerConsentLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客协议同意记录列表
     *
     * @param bo 查询条件
     * @return 乘客协议同意记录列表
     */
    @Override
    public List<MbrPassengerConsentLogVo> queryList(MbrPassengerConsentLogBo bo) {
        LambdaQueryWrapper<MbrPassengerConsentLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrPassengerConsentLog> buildQueryWrapper(MbrPassengerConsentLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrPassengerConsentLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPassengerId() != null, MbrPassengerConsentLog::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), MbrPassengerConsentLog::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getScene()), MbrPassengerConsentLog::getScene, bo.getScene());
        lqw.like(StringUtils.isNotBlank(bo.getName()), MbrPassengerConsentLog::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), MbrPassengerConsentLog::getIp, bo.getIp());
        lqw.eq(bo.getConsentTime() != null, MbrPassengerConsentLog::getConsentTime, bo.getConsentTime());
        lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), MbrPassengerConsentLog::getConsentTime, bo.getStartTime());
        lqw.le(StringUtils.isNotBlank(bo.getEndTime()), MbrPassengerConsentLog::getConsentTime, bo.getEndTime());
        lqw.orderByDesc(MbrPassengerConsentLog::getConsentTime);
        return lqw;
    }

    /**
     * 新增乘客协议同意记录
     *
     * @param bo 乘客协议同意记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MbrPassengerConsentLogBo bo) {
        MbrPassengerConsentLog add = MapstructUtils.convert(bo, MbrPassengerConsentLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客协议同意记录
     *
     * @param bo 乘客协议同意记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MbrPassengerConsentLogBo bo) {
        MbrPassengerConsentLog update = MapstructUtils.convert(bo, MbrPassengerConsentLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MbrPassengerConsentLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客协议同意记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
