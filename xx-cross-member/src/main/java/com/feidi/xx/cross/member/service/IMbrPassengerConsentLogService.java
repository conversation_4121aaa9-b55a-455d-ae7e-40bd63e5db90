package com.feidi.xx.cross.member.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerConsentLogBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerConsentLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客协议同意记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface IMbrPassengerConsentLogService {

    /**
     * 查询乘客协议同意记录
     *
     * @param id 主键
     * @return 乘客协议同意记录
     */
    MbrPassengerConsentLogVo queryById(Long id);

    /**
     * 分页查询乘客协议同意记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客协议同意记录分页列表
     */
    TableDataInfo<MbrPassengerConsentLogVo> queryPageList(MbrPassengerConsentLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客协议同意记录列表
     *
     * @param bo 查询条件
     * @return 乘客协议同意记录列表
     */
    List<MbrPassengerConsentLogVo> queryList(MbrPassengerConsentLogBo bo);

    /**
     * 新增乘客协议同意记录
     *
     * @param bo 乘客协议同意记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MbrPassengerConsentLogBo bo);

    /**
     * 修改乘客协议同意记录
     *
     * @param bo 乘客协议同意记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MbrPassengerConsentLogBo bo);

    /**
     * 校验并批量删除乘客协议同意记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
