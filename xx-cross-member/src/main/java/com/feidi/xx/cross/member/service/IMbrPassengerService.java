package com.feidi.xx.cross.member.service;

import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBlacklistBo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBo;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerLoginVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客Service接口
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
public interface IMbrPassengerService {

    /**
     * 查询乘客
     */
    MbrPassengerVo queryById(Long id);

    /**
     * 查询乘客列表
     */
    TableDataInfo<MbrPassengerVo> queryPageList(MbrPassengerBo bo, PageQuery pageQuery);

    /**
     * 查询乘客列表
     */
    List<MbrPassengerVo> queryList(MbrPassengerBo bo);

    /**
     * 新增乘客
     */
    Boolean insertByBo(MbrPassengerBo bo);

    /**
     * 修改乘客
     */
    Boolean updateByBo(MbrPassengerBo bo);

    /**
     * 校验并批量删除乘客信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 乘客登录
     *
     * @param remotePassengerLoginVo
     * @return
     */
    XcxLoginUser login(RemotePassengerLoginVo remotePassengerLoginVo);

    /**
     * 获取乘客信息
     *
     * @param remotePassengerLoginVo
     * @return
     */
    RemotePassengerVo getPassengerInfo(RemotePassengerLoginVo remotePassengerLoginVo);

    Long getDriverIdByDriverCode(String driverCode);

    /**
     * 紧急联系人
     */
    Boolean emergency(MbrPassengerBo bo);

    /**
     * 紧急联系人
     */
    Emergency getEmergency(Long passengerId);

    /**
     * 查询乘客邀请列表
     *
     * @return
     */
    List<MbrPassengerVo> queryInviteList();

    /**
     * 同意服务告知函
     *
     * @param passengerId 乘客id
     */
    void agreedServiceNotice(Long passengerId);

    /**
     * 拉黑
     *
     * @param bo
     * @return
     */
    boolean block(MbrPassengerBlacklistBo bo);

    /**
     * 注册
     *
     * @param remotePassengerLoginVo
     * @return
     */
    MbrPassenger register(RemotePassengerLoginVo remotePassengerLoginVo);
}