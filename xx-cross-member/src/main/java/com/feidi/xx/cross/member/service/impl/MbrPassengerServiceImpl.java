package com.feidi.xx.cross.member.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.market.ActivityTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.passenger.AgreementStatus;
import com.feidi.xx.cross.common.enums.passenger.PassengerConsentSceneEnum;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.market.api.RemoteActivityService;
import com.feidi.xx.cross.market.api.domain.RemoteActivityBo;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.MbrPassengerConsentLog;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBlacklistBo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBo;
import com.feidi.xx.cross.member.domain.bo.MbrVoucherBo;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerBlacklistVo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.member.mapper.MbrPassengerConsentLogMapper;
import com.feidi.xx.cross.member.mapper.MbrPassengerMapper;
import com.feidi.xx.cross.member.mq.PassengerRegisterProducer;
import com.feidi.xx.cross.member.service.IMbrPassengerBlacklistService;
import com.feidi.xx.cross.member.service.IMbrPassengerService;
import com.feidi.xx.cross.member.service.IMbrVoucherService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerLoginVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteAgentUserService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentUserVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.log.api.RemoteLogService;
import com.feidi.xx.log.api.domain.vo.RemoteLogLoginVo;
import com.feidi.xx.system.api.RemoteDistrictService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 乘客Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@RequiredArgsConstructor
@Service
public class MbrPassengerServiceImpl implements IMbrPassengerService {

    private static final Logger log = LoggerFactory.getLogger(MbrPassengerServiceImpl.class);
    private final MbrPassengerMapper baseMapper;
    private final IMbrVoucherService voucherService;
    private final ScheduledExecutorService scheduledExecutorService;

    private final MktCacheManager mktCacheManager;

    private final MbrPassengerConsentLogMapper consentLogMapper;

    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteAgentUserService remoteAgentUserService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteActivityService remoteActivityService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteLogService remoteLogService;
    private final OrdCacheManager ordCacheManager;

    @DubboReference
    private final RemoteDistrictService reversedDistrictService;

    private final IMbrPassengerBlacklistService passengerBlacklistService;

    private final PassengerRegisterProducer passengerRegisterProducer;

    /**
     * 查询乘客
     */
    @Override
    public MbrPassengerVo queryById(Long id) {
        MbrPassengerVo passengerVo = baseMapper.selectVoById(id);
        Assert.notNull(passengerVo, "用户不存在");
        String openId = voucherService.getOpenId(id);
        passengerVo.setOpenId(openId);
        passengerVo.setUserType(LoginHelper.getUserType().getUserType());
        return passengerVo;
    }

    /**
     * 查询乘客列表
     */
    @Override
    public TableDataInfo<MbrPassengerVo> queryPageList(MbrPassengerBo bo, PageQuery pageQuery) {
        //初始化
        if (com.feidi.xx.common.core.utils.ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (com.feidi.xx.common.core.utils.ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<MbrPassenger> lqw = buildQueryWrapper(bo);
        if (!StringUtils.isEmpty(bo.getLogLocation())) {
            List<RemoteLogLoginVo> remoteLogLoginVos = remoteLogService.queryLatestLoginLogByLogLocation(bo.getLogLocation());
            Set<String> logUserName = remoteLogLoginVos.stream().map(RemoteLogLoginVo::getUserName).collect(Collectors.toSet());
            lqw.in(CollUtils.isNotEmpty(logUserName), MbrPassenger::getPhone, logUserName);
            if (CollUtils.isEmpty(logUserName)) {
                lqw.last("limit 0");
            }
        }
        Page<MbrPassengerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 代理商id集合
        Set<Long> agentIds = result.getRecords().stream().map(MbrPassengerVo::getAgentId).collect(Collectors.toSet());
        // 司机id集合
        Set<Long> driverIds = result.getRecords().stream().map(MbrPassengerVo::getDriverId).collect(Collectors.toSet());
        // 乘客id集合
        List<Long> passengerIds = result.getRecords().stream().map(MbrPassengerVo::getId).toList();

        Set<String> phones = result.getRecords().stream().map(MbrPassengerVo::getPhone).collect(Collectors.toSet());

        Map<String, RemoteLogLoginVo> phone2LoginLogMap = remoteLogService.queryLatestLoginLogByUserNames(new ArrayList<>(phones))
                .stream().collect(Collectors.toMap(RemoteLogLoginVo::getUserName, Function.identity(), (v1, v2) -> v2));
        Map<Long, RemoteAgentVo> agentInfoMap = remoteAgentService.getAgentInfoById(new ArrayList<>(agentIds))
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity(), (v1, v2) -> v2));
        Map<Long, RemoteDriverVo> driverInfoMap = remoteDriverService.getDriverByIds(new ArrayList<>(driverIds))
                .stream().collect(Collectors.toMap(RemoteDriverVo::getId, Function.identity(), (v1, v2) -> v2));
        Map<Long, List<RemoteOrderVo>> passengerInfoMap = remoteOrderService.getOrderByPassengerIds(passengerIds)
                .stream().collect(Collectors.groupingBy(RemoteOrderVo::getPassengerId));

        result.getRecords().forEach(
                item -> {
                    item.setPhoneEnd(OrderUtils.getPassengerName(item.getPhone()));
                    if (ObjectUtil.isNotNull(item.getAgentId()) && agentInfoMap.containsKey(item.getAgentId())) {
                        item.setCompanyName(agentInfoMap.get(item.getAgentId()).getCompanyName());
                    }
                    if (ObjectUtil.isNotNull(item.getDriverId()) && driverInfoMap.containsKey(item.getDriverId())) {
                        item.setDriverName(driverInfoMap.get(item.getDriverId()).getName());
                    }
                    item.setOrderNum(0);
                    item.setFinishNum(0);
                    if (passengerInfoMap.containsKey(item.getId())) {
                        List<RemoteOrderVo> remoteOrderVo = passengerInfoMap.get(item.getId());
                        List<RemoteOrderVo> finishOrders = remoteOrderVo.stream().filter(order -> Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())).toList();
                        item.setOrderNum(remoteOrderVo.size());
                        item.setFinishNum(CollUtils.isNotEmpty(finishOrders) ? finishOrders.size() : 0);
                    }
                    if (phone2LoginLogMap.containsKey(item.getPhone())) {
                        String loginLocation = phone2LoginLogMap.get(item.getPhone()).getLoginLocation();
                        if (StringUtils.isNotBlank(loginLocation) && loginLocation.lastIndexOf("|") > 0) {
                            item.setLoginLocation(loginLocation.substring(0, loginLocation.lastIndexOf("|")));
                        }
                        item.setLoginTime(phone2LoginLogMap.get(item.getPhone()).getLoginTime());
                    }
                }
        );
        return TableDataInfo.build(result);
    }

    /**
     * 查询乘客列表
     */
    @Override
    public List<MbrPassengerVo> queryList(MbrPassengerBo bo) {
        LambdaQueryWrapper<MbrPassenger> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrPassenger> buildQueryWrapper(MbrPassengerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrPassenger> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getParentId() != null, MbrPassenger::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getPhone()), MbrPassenger::getPhone, bo.getPhone());
        lqw.like(StringUtils.isNotBlank(bo.getName()), MbrPassenger::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCardNo()), MbrPassenger::getCardNo, bo.getCardNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), MbrPassenger::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MbrPassenger::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSource()), MbrPassenger::getSource, bo.getSource());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), MbrPassenger::getCode, bo.getCode());
        lqw.ge(StringUtils.isNotBlank(bo.getStartTime()), MbrPassenger::getCreateTime, bo.getStartTime());
        lqw.le(StringUtils.isNotBlank(bo.getEndTime()), MbrPassenger::getCreateTime, bo.getEndTime());
        lqw.eq(ObjectUtils.isNotNull(bo.getDriverId()) && bo.getDriverId() > 0, MbrPassenger::getDriverId, bo.getDriverId());
        lqw.eq(ObjectUtils.isNotNull(bo.getAgentId()) && bo.getAgentId() > 0, MbrPassenger::getAgentId, bo.getAgentId());
        return lqw;
    }

    /**
     * 新增乘客
     */
    @Override
    public Boolean insertByBo(MbrPassengerBo bo) {
        MbrPassenger add = MapstructUtils.convert(bo, MbrPassenger.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客
     */
    @Override
    public Boolean updateByBo(MbrPassengerBo bo) {
        MbrPassenger update = MapstructUtils.convert(bo, MbrPassenger.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MbrPassenger entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除乘客
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据手机号获取用户信息
     *
     * @param tenantId
     * @param phone
     * @return
     */
    private MbrPassengerVo getPassengerByPhone(String tenantId, String phone) {
        LambdaQueryWrapper<MbrPassenger> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MbrPassenger::getPhone, phone)
                .eq(MbrPassenger::getTenantId, tenantId);
        return baseMapper.selectVoOne(queryWrapper);
    }

    /**
     * 注册 - 重构
     *
     * @param remotePassengerLoginVo
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public MbrPassenger register(RemotePassengerLoginVo remotePassengerLoginVo) {
        MbrPassenger passenger = new MbrPassenger();

        passenger.setTenantId(remotePassengerLoginVo.getTenantId());
        passenger.setPhone(remotePassengerLoginVo.getPhone());
        passenger.setSource(remotePassengerLoginVo.getSource());
        passenger.setStatus(UserStatusEnum.OK.getCode());
        passenger.setChannel(remotePassengerLoginVo.getChannel());
        passenger.setCode(makeCode());

        //TODO 邀请逻辑后续处理
        if (UserTypeEnum.AGENT_USER.getUserType().equals(remotePassengerLoginVo.getInviteType())) {
            RemoteAgentUserVo agentInfo = null;
            if (StringUtils.isNotBlank(remotePassengerLoginVo.getInviteCode())) {
                agentInfo = remoteAgentUserService.getAgentInfoByCode(remotePassengerLoginVo.getInviteCode());
            }
            if (agentInfo != null) {
                passenger.setAgentId(agentInfo.getAgentId());
            }
        } else if (UserTypeEnum.DRIVER_USER.getUserType().equals(remotePassengerLoginVo.getInviteType())) {
            RemoteDriverVo driverInfo = remoteDriverService.getDriverInfoByCode(remotePassengerLoginVo.getInviteCode());
            if (driverInfo != null) {
                passenger.setDriverId(driverInfo.getId());
                // 代理商信息
                if (ArithUtils.isNotNull(driverInfo.getAgentId())) {
                    RemoteAgentVo agentInfo = remoteAgentService.getAgentInfoById(driverInfo.getAgentId());
                    if (agentInfo != null) {
                        passenger.setAgentId(agentInfo.getId());
                    }
                }
            }
        }
        /*else if (UserTypeEnum.PASSENGER_USER.getUserType().equals(remotePassengerLoginVo.getInviteType())) {
            MbrPassengerVo passengerInfo = getPassengerInfo(remotePassengerLoginVo.getInviteCode());
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(passengerInfo)) {
                passenger.setParentId(passengerInfo.getId());
            }
        }*/
        baseMapper.insert(passenger);
        RemoteActivityBo remoteActivityBo = new RemoteActivityBo();
        remoteActivityBo.setActivityType(ActivityTypeEnum.NEW_USER.getCode());
        remoteActivityBo.setAgentId(passenger.getAgentId());
        remoteActivityBo.setTenantId(remotePassengerLoginVo.getTenantId());
        remoteActivityBo.setCityCode(remotePassengerLoginVo.getCityCode());

        remoteActivityBo.setPassengerId(passenger.getId());
        remoteActivityService.joinActivity(remoteActivityBo);
        // 乘客注册成功事件
        passengerRegisterProducer.sendMessage(create(remotePassengerLoginVo)
                .setPassengerId(passenger.getId()));
        return passenger;
    }

    public static PassengerRegisterEvent create(RemotePassengerLoginVo remotePassengerLoginVo) {
        return PassengerRegisterEvent.builder()
                .appId(remotePassengerLoginVo.getAppId())
                .openId(remotePassengerLoginVo.getOpenId())
                .unionId(remotePassengerLoginVo.getUnionId())
                .tenantId(remotePassengerLoginVo.getTenantId())
                .phone(remotePassengerLoginVo.getPhone())
                .registerTime(new Date())
                .cityCode(remotePassengerLoginVo.getCityCode())
                .inviteCode(remotePassengerLoginVo.getInviteCode())
                .inviteType(remotePassengerLoginVo.getInviteType())
                .source(remotePassengerLoginVo.getSource())
                .channel(remotePassengerLoginVo.getChannel())
                .type(remotePassengerLoginVo.getType())
                .build();
    }

    /**
     * 生成邀请码
     *
     * @return
     */
    private String makeCode() {
        String code = RandomUtil.randomString(8);
        MbrPassenger exists = baseMapper.getByCode(code);
        if (ObjectUtils.isNotNull(exists)) {
            return makeCode();
        }
        return code;
    }

    /**
     * 根据邀请码查询乘客信息
     *
     * @param inviteCode 邀请码
     * @return
     */
    public MbrPassengerVo getPassengerInfo(String inviteCode) {
        LambdaQueryWrapper<MbrPassenger> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MbrPassenger::getCode, inviteCode);
        return baseMapper.selectVoOne(queryWrapper);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public XcxLoginUser login(RemotePassengerLoginVo remotePassengerLoginVo) {
        XcxLoginUser loginUser = new XcxLoginUser();
        /// 手机号是否存在
        MbrPassengerVo passenger = getPassengerByPhone(remotePassengerLoginVo.getTenantId(), remotePassengerLoginVo.getPhone());
        MbrPassengerConsentLog mbrPassengerConsentLog = new MbrPassengerConsentLog();
        mbrPassengerConsentLog.setScene(PassengerConsentSceneEnum.LOGIN.getCode());
        if (ObjectUtils.isNull(passenger)) {
            /// 注册
            MbrPassenger register = register(remotePassengerLoginVo);
            passenger = BeanUtils.copyProperties(register, MbrPassengerVo.class);
            mbrPassengerConsentLog.setScene(PassengerConsentSceneEnum.REGISTER.getCode());
        }
        if (ObjectUtils.isNull(passenger)) {
            throw new ServiceException("登录异常");
        }
        // 保存凭证
        saveVoucher(remotePassengerLoginVo, passenger);
        saveConsentLog(mbrPassengerConsentLog, passenger, remotePassengerLoginVo.getIp());
        loginUser.setOpenid(remotePassengerLoginVo.getOpenId());
        loginUser.setUserId(passenger.getId());
        loginUser.setNickname(passenger.getName());
        loginUser.setUsername(passenger.getName());
        loginUser.setUserPhone(passenger.getPhone());
        loginUser.setTenantId(remotePassengerLoginVo.getTenantId());
        loginUser.setUserType(UserTypeEnum.PASSENGER_USER.getUserType());
        return loginUser;
    }

    private void saveConsentLog(MbrPassengerConsentLog mbrPassengerConsentLog, MbrPassengerVo passenger, String ip){
        try{
            mbrPassengerConsentLog.setName("《喜行出行平台用户协议》、《喜行城际出行用户服务协议》、《隐私政策协议》");
            mbrPassengerConsentLog.setPassengerId(passenger.getId());
            mbrPassengerConsentLog.setPhone(passenger.getPhone());
            mbrPassengerConsentLog.setIp(ip);
            mbrPassengerConsentLog.setConsentTime(new Date());
            mbrPassengerConsentLog.setTenantId("000000");
            consentLogMapper.insert(mbrPassengerConsentLog);
        }catch(Exception e){
            log.error("新增协议同意记录失败", e);
        }
    }

    private void saveVoucher(RemotePassengerLoginVo remotePassengerLoginVo, MbrPassengerVo passenger) {
        MbrVoucherBo MbrVoucherBo = new MbrVoucherBo();
        MbrVoucherBo.setPassengerId(passenger.getId());
        MbrVoucherBo.setAppId(remotePassengerLoginVo.getAppId());
        MbrVoucherBo.setType(remotePassengerLoginVo.getType());
        MbrVoucherBo.setOpenId(remotePassengerLoginVo.getOpenId());
        MbrVoucherBo.setUnionId(remotePassengerLoginVo.getUnionId());
        MbrVoucherBo.setStatus(StatusEnum.ENABLE.getCode());
        voucherService.insertByBo(MbrVoucherBo);
    }

    @Override
    public RemotePassengerVo getPassengerInfo(RemotePassengerLoginVo remotePassengerLoginVo) {
        /// 手机号是否存在
        MbrPassengerVo passenger = getPassengerByPhone(remotePassengerLoginVo.getTenantId(), remotePassengerLoginVo.getPhone());
        if (ObjectUtils.isNull(passenger)) {
            /// 注册
            MbrPassenger register = register(remotePassengerLoginVo);
            /// 代客下单缓存
            mktCacheManager.bindPassenger(remotePassengerLoginVo.getType(), remotePassengerLoginVo.getInviteUserId(), register.getId());

            passenger = BeanUtils.copyProperties(register, MbrPassengerVo.class);
        }
        return BeanUtils.copyProperties(passenger, RemotePassengerVo.class);
    }

    @Override
    public Long getDriverIdByDriverCode(String driverCode) {
        try {
            log.info("邀请码{}!", driverCode);
            if (StringUtils.isNotBlank(driverCode)) {
                RemoteDriverVo driverInfo = remoteDriverService.getDriverInfoByCode(driverCode);
                if (driverInfo != null) {
                    log.info("司机信息{}!", driverInfo);
                    Long userId = LoginHelper.getUserId();
                    log.info("用户id:{}!", userId);
                    ordCacheManager.addInviteCode(userId, String.valueOf(driverInfo.getId()));
                    return driverInfo.getId();
                }
            }
            return null;

        } catch (Exception e) {
            log.info("邀请码异常{}!", driverCode);
        }
        return null;
    }

    @Override
    public Boolean emergency(MbrPassengerBo bo) {
        MbrPassenger passenger = baseMapper.selectById(bo.getId());
        if (passenger != null) {
            Emergency emergency = passenger.getEmergency();
            if (emergency == null) {
                emergency = new Emergency();
            }
            if (bo.getEmergency() != null) {
                emergency.setName(bo.getEmergency().getName());
                emergency.setPhone(bo.getEmergency().getPhone());
            }
            passenger.setEmergency(emergency);
            baseMapper.updateById(passenger);
        }
        return true;
    }

    @Override
    public Emergency getEmergency(Long passengerId) {
        MbrPassenger passenger = baseMapper.selectById(passengerId);
        if (passenger != null) {
            return passenger.getEmergency();
        }
        return null;
    }

    /**
     * 查询乘客邀请列表
     *
     * @return
     */
    @Override
    public List<MbrPassengerVo> queryInviteList() {
        LambdaQueryWrapper<MbrPassenger> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MbrPassenger::getParentId, LoginHelper.getUserId());
        return baseMapper.selectVoList(queryWrapper);
    }

    /**
     * 同意服务告知函
     *
     * @param passengerId 乘客id
     */
    @Override
    @Transactional
    public void agreedServiceNotice(Long passengerId) {
        var wrapper = Wrappers.<MbrPassenger>lambdaUpdate()
                .eq(MbrPassenger::getId, passengerId)
                .set(MbrPassenger::getAgreedToServiceNotice, AgreementStatus.AGREED.getCode())
                .set(MbrPassenger::getTermsAgreedAt, new Date())
                .set(MbrPassenger::getUpdateTime, new Date());
        boolean b = baseMapper.update(wrapper) > 0;

        if (!b) {
            log.error("乘客同意服务协议失败, id {}", passengerId);
            throw new ServiceException("乘客同意服务协议失败");
        }

        try {
            MbrPassengerConsentLog mbrPassengerConsentLog = new MbrPassengerConsentLog();
            mbrPassengerConsentLog.setPassengerId(passengerId);
            mbrPassengerConsentLog.setPhone(LoginHelper.getUserPhone());
            mbrPassengerConsentLog.setScene(PassengerConsentSceneEnum.NOTICE.getCode());
            mbrPassengerConsentLog.setName("《服务告知函》");
            mbrPassengerConsentLog.setIp(ServletUtils.getClientIP());
            mbrPassengerConsentLog.setConsentTime(new Date());
            mbrPassengerConsentLog.setTenantId("000000");
            consentLogMapper.insert(mbrPassengerConsentLog);
        }catch (Exception e){
            log.error("新增协议记录失败", e);
        }
    }


    @Override
    public boolean block(MbrPassengerBlacklistBo bo) {
        MbrPassenger passenger = baseMapper.selectById(bo.getPassengerId());
        if (ObjectUtils.isNull(passenger)) {
            throw new ServiceException("用户不存在");
        }
        MbrPassengerBlacklistVo mbrPassengerBlacklistVo = passengerBlacklistService.queryByPassengerId(bo.getPassengerId());
        if (ObjectUtils.isNotNull(mbrPassengerBlacklistVo) && IsYesEnum.YES.getCode().equals(bo.getBlacklisted())) {
            throw new ServiceException("用户已被拉黑");
        }
        var wrapper = Wrappers.<MbrPassenger>lambdaUpdate()
                .eq(MbrPassenger::getId, bo.getPassengerId())
                .set(MbrPassenger::getBlacklisted, bo.getBlacklisted());
        baseMapper.update(wrapper);

        if (bo.getBlacklisted().equals(IsYesEnum.YES.getCode())) {
            return passengerBlacklistService.insertByBo(bo);
        } else if (mbrPassengerBlacklistVo != null) {
            bo.setId(mbrPassengerBlacklistVo.getId());
            return passengerBlacklistService.updateByBo(bo);
        }
        return true;
    }
}
