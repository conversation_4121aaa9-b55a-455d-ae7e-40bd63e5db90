package com.feidi.xx.cross.member.controller.admin;

import java.util.List;

import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerConsentLogBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerConsentLogVo;
import com.feidi.xx.cross.member.service.IMbrPassengerConsentLogService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 乘客协议同意记录
 * 前端访问路由地址为:/settle/passengerConsentLog
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passengerConsentLog")
public class MbrPassengerConsentLogController extends BaseController {

    private final IMbrPassengerConsentLogService mbrPassengerConsentLogService;

    /**
     * 查询乘客协议同意记录列表
     */
    @SaCheckPermission("settle:passengerConsentLog:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MbrPassengerConsentLogVo> list(MbrPassengerConsentLogBo bo, PageQuery pageQuery) {
        return mbrPassengerConsentLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出乘客协议同意记录列表
     */
    @SaCheckPermission("settle:passengerConsentLog:export")
    @Log(title = "乘客协议同意记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MbrPassengerConsentLogBo bo, HttpServletResponse response) {
        List<MbrPassengerConsentLogVo> list = mbrPassengerConsentLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "乘客协议同意记录", MbrPassengerConsentLogVo.class, response);
    }

    /**
     * 获取乘客协议同意记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("settle:passengerConsentLog:query")
    @GetMapping("/{id}")
    @Enum2TextAspect
    public R<MbrPassengerConsentLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mbrPassengerConsentLogService.queryById(id));
    }

    /**
     * 新增乘客协议同意记录
     */
    @SaCheckPermission("settle:passengerConsentLog:add")
    @Log(title = "乘客协议同意记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MbrPassengerConsentLogBo bo) {
        return toAjax(mbrPassengerConsentLogService.insertByBo(bo));
    }

    /**
     * 修改乘客协议同意记录
     */
    @SaCheckPermission("settle:passengerConsentLog:edit")
    @Log(title = "乘客协议同意记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrPassengerConsentLogBo bo) {
        return toAjax(mbrPassengerConsentLogService.updateByBo(bo));
    }

    /**
     * 删除乘客协议同意记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("settle:passengerConsentLog:remove")
    @Log(title = "乘客协议同意记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mbrPassengerConsentLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
