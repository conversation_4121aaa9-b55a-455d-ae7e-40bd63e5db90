package com.feidi.xx.cross.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 乘客协议同意记录对象 mbr_passenger_consent_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mbr_passenger_consent_log")
public class MbrPassengerConsentLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 业务场景[PassengerConsentSceneEnum]
     */
    private String scene;

    /**
     * 协议名称
     */
    private String name;

    /**
     * ip
     */
    private String ip;

    /**
     * 同意时间
     */
    private Date consentTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
