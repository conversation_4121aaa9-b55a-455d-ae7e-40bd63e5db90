package com.feidi.xx.cross.member.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.member.domain.MbrPassengerConsentLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 乘客协议同意记录业务对象 mbr_passenger_consent_log
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrPassengerConsentLog.class, reverseConvertGenerate = false)
public class MbrPassengerConsentLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机id
     */
    @NotNull(message = "乘客id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerId;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 业务场景
     */
    @NotBlank(message = "业务场景不能为空", groups = { AddGroup.class, EditGroup.class })
    private String scene;

    /**
     * 协议名称
     */
    @NotBlank(message = "协议名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * ip
     */
    @NotBlank(message = "ip不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ip;

    /**
     * 同意时间
     */
    @NotNull(message = "同意时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date consentTime;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


}
