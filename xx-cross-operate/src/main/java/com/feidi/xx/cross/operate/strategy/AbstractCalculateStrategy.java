package com.feidi.xx.cross.operate.strategy;


import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.vo.OprPriceCacheVo;
import com.feidi.xx.cross.common.enums.operate.HolidayTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantBo;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import com.feidi.xx.cross.operate.api.domain.price.dto.FixedDataDto;
import com.feidi.xx.cross.operate.api.domain.price.dto.InitiateDataDto;
import com.feidi.xx.cross.operate.api.domain.price.dto.MileageDataDto;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.operate.domain.OprPriceDetail;
import com.feidi.xx.cross.operate.domain.bo.OprEstimateRecordBo;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.service.IOprEstimateRecordService;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service
public abstract class AbstractCalculateStrategy implements CalculateStrategy {

    @Resource
    private  IOprPriceDetailService priceDetailService;
    @Resource
    private  IOprEstimateRecordService estimateRecordService;
    @Resource
    private  ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private  RemoteCouponGrantService couponGrantService;

    /**
     * 获取价格配置
     *
     * @param calculateDTO
     * @return
     */
    public OprPriceCacheVo getRelatedPricingTemplate(OprCalculateDTO calculateDTO) {
        //远程调用
        return getOprPricing(calculateDTO);
    }

    @Nullable
    private OprPriceCacheVo getOprPricing(OprCalculateDTO calculateDTO) {
        OprPriceCacheVo priceCacheVo;
        String priceKey = buildKey(calculateDTO);
        if (calculateDTO.getHolidayType().equals(HolidayTypeEnum.HOLIDAY_PRICE.getCode())) {
            if (!RedisUtils.hasKey(priceKey)) {
                OprPriceDetail oprPriceDetail = priceDetailService.getPricingByCondition(calculateDTO);
                if (ObjectUtils.isNotEmpty(oprPriceDetail)) {
                    return  BeanUtils.copyProperties(oprPriceDetail, OprPriceCacheVo.class);
                } else {
                    calculateDTO.setHolidayType(HolidayTypeEnum.WEEKDAY_PRICE.getCode());
                    priceKey = buildKey(calculateDTO);
                }
            }
        }
        if (RedisUtils.hasKey(priceKey)) {
            priceCacheVo = RedisUtils.getCacheObject(priceKey);
        } else {
            OprPriceDetail oprPriceDetail = priceDetailService.getPricingByCondition(calculateDTO);
            priceCacheVo = BeanUtils.copyProperties(oprPriceDetail, OprPriceCacheVo.class);
            //加入缓存
            RedisUtils.setCacheObject(priceKey, priceCacheVo, OprCacheKeyEnum.OPR_PRICING_INFO_KEY.getDuration());
        }
        if (priceCacheVo == null) {
            log.error("价目表未配置!");
        }
        return priceCacheVo;
    }


    /**
     * 构建key
     *
     * @param calculateDTO 计价入参
     * @return
     */
    @NotNull
    private String buildKey(OprCalculateDTO calculateDTO) {
        String key = OprCacheKeyEnum.OPR_PRICING_INFO_KEY.create(PlatformCodeEnum.TY.getCode(), calculateDTO.getPriceId(), calculateDTO.getProductCode(), calculateDTO.getHolidayType());
        return key;
    }

    /**
     * 计算价格方法
     *
     * @param calculateDTO   询价参数
     * @param pricingCacheVo 价目模版
     * @return 返回估价参数
     */
    public PriceDto calculateMileagePrices(OprCalculateDTO calculateDTO, OprPriceCacheVo pricingCacheVo) {
        PriceDto priceDto = new PriceDto();
        priceDto.setProductCode(calculateDTO.getProductCode());
        // 里程 公里
        Integer mileage = calculateDTO.getMileage();
        // 里程金额
        Long mileagePrice = 0L;
        // 长途费金额
        //起步价格模版
        // 获取当前时间的起步价目参数
        // 获取特定 passengerCount 对应的 initiateData 字符串
        String initiateData = new JSONObject(pricingCacheVo.getInitiateData())
                .getStr(calculateDTO.getPassengerCount()
                        .toString());
        // 直接解析为 InitiateDataDTO 对象
        InitiateDataDto initiateDataDTO = JSON.parseObject(initiateData, InitiateDataDto.class);
        // 设置起步价
        priceDto.setInitPrice(initiateDataDTO.getStartPrice());

        // 1. 判断是否超过起步距离
        if (mileage > initiateDataDTO.getStartMileage()) {

            String mileageData = new JSONObject(pricingCacheVo.getMileageData())
                    .getStr(calculateDTO.getPassengerCount()
                            .toString());
            //价目配置列表
            List<MileageDataDto> mileageDataDtos = JSON.parseArray(mileageData, MileageDataDto.class);
            //获取符合费用的模版
            List<MileageDataDto> mileageDataDtoList = getMileageDistancePricingTemplate(mileageDataDtos, mileage);
            // 计算里程费用
            mileagePrice = calculateMileageDistancePrice(mileageDataDtoList, mileage);
            priceDto.setMileagePrice(mileagePrice);
        }
        // 汇总计算
        return calculateAndSetPrices(priceDto);
    }

    /**
     * 一口价计算方法
     */
    public PriceDto calculateAndSetPrices(OprCalculateDTO calculateDTO, OprPriceCacheVo pricingCacheVo) {
        PriceDto priceDto = new PriceDto();
        priceDto.setProductCode(calculateDTO.getProductCode());
        String initiateData = new JSONObject(pricingCacheVo.getInitiateData())
                .getStr(calculateDTO.getPassengerCount()
                        .toString());
        FixedDataDto fixedDto = JSON.parseObject(initiateData, FixedDataDto.class);
        priceDto.setTotalPrice(fixedDto.getPrice());
        priceDto.setCalculatePrice(fixedDto.getPrice());
        return priceDto;
    }

    /**
     * 初始化距离模版
     *
     * @param mileageDataDtos 距离模板配置列表
     * @return
     **/
    public List<MileageDataDto> getMileageDistancePricingTemplate(List<MileageDataDto> mileageDataDtos, Integer mileage) {
        /**
         * 距离计算
         */
        List<MileageDataDto> filteredAndSorted = mileageDataDtos.stream()
                .filter(dto -> dto.getStart() * 1000 < mileage)
                .sorted(Comparator.comparingDouble(MileageDataDto::getStart))
                .collect(Collectors.toList());
        return filteredAndSorted;
    }

    /**
     * 计算里程费
     *
     * @param dataDTOS 里程价目配置列表
     * @param mileage  里程
     **/

    public Long calculateMileageDistancePrice(List<MileageDataDto> dataDTOS, Integer mileage) {
        Double mileagePrice = 0.0;
        Double longMileage = 0.0;
        for (MileageDataDto dataDTO : dataDTOS) {
            Double start = dataDTO.getStart() * 1000;
            Double end = dataDTO.getEnd() * 1000;
            if (mileage > dataDTO.getStart()) {
                /**
                 * 剩余里程
                 **/
                //减去最大里程
                if (mileage > end && end != 0) {
                    longMileage = ArithUtils.sub(end, start);
                } else {
                    longMileage = ArithUtils.sub(mileage, start);
                }
                /**
                 * 长途里程计算金额
                 **/
                Double mulled = ArithUtils.mul(longMileage, dataDTO.getUnit());
                mileagePrice = ArithUtils.add(mileagePrice, mulled);
            }
        }
        mileagePrice = ArithUtils.div(mileagePrice, 1000);
        return mileagePrice.longValue();
    }

    /*
     * 设置返回视图
     *
     * @param OprCalculateVo
     **/
    public PriceDto calculateAndSetPrices(PriceDto priceDto) {
        Long estimatePrice = 0L;

        //计算总金额   总金额（单位：分）【initPrice+mileagePrice】
        Long totalPrice = ArithUtils.add(priceDto.getInitPrice(), priceDto.getMileagePrice());
        priceDto.setTotalPrice(totalPrice);
        //计算预估金额
        estimatePrice = totalPrice;
        //优惠金额
        priceDto.setCalculatePrice(estimatePrice);
        return priceDto;
    }

    /**
     * 询价记录保存
     *
     * @return
     */
    public void saveEstimateRecord(OprCalculateDTO calculateDTO, OprCalculateVo oprCalculateVo) {
        log.info("询价记录保存线路id:{}", calculateDTO.getLineId());
        OprEstimateRecordBo oprEstimateRecordBo = BeanUtils.copyProperties(calculateDTO, OprEstimateRecordBo.class);
        oprEstimateRecordBo.setTenantId(LoginHelper.getTenantId());
        oprEstimateRecordBo.setCreateBy(LoginHelper.getUserId());
        oprEstimateRecordBo.setUpdateBy(LoginHelper.getUserId());
        oprEstimateRecordBo.setCreateTime(DateUtils.getNowDate());
        oprEstimateRecordBo.setUpdateTime(DateUtils.getNowDate());
        oprEstimateRecordBo.setCreateDept(LoginHelper.getDeptId());
        oprEstimateRecordBo.setEarliestTime(DateUtils.convertLongToDate(calculateDTO.getStartTime()));
        oprEstimateRecordBo.setLatestTime(DateUtils.convertLongToDate(calculateDTO.getEndTime()));
        oprEstimateRecordBo.setPassengerNum(calculateDTO.getPassengerCount());
        oprEstimateRecordBo.setLineId(calculateDTO.getLineId());
        // 询价结果
        oprEstimateRecordBo.setPrice(oprCalculateVo.getPriceDtoList());

        //计算运行时间
        long startTime = DateUtils.getUnixTimeStamps();
        estimateRecordService.saveEstimateRecord(oprEstimateRecordBo);
        long endTime = DateUtils.getUnixTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("询价记录价格 -  耗时：【{}】", ArithUtils.sub(endTime, startTime));

        }

    }

    /**
     * 优惠卷处理
     */
    public void discountCoupon(OprCalculateDTO calculateDTO, OprCalculateVo oprCalculateVo) {
        if (ObjectUtils.isNotEmpty(calculateDTO.getPassengerId())) {
            for (PriceDto priceDto : oprCalculateVo.getPriceDtoList()) {
                RemoteCouponGrantBo remoteCouponGrantBo = new RemoteCouponGrantBo();
                remoteCouponGrantBo.setPassengerId(calculateDTO.getPassengerId());
                remoteCouponGrantBo.setLineId(calculateDTO.getLineId() != null ? calculateDTO.getLineId().toString() : null);
                remoteCouponGrantBo.setCityCode(calculateDTO.getStartCityCode());
                remoteCouponGrantBo.setOrderPrice(priceDto.getCalculatePrice());
                remoteCouponGrantBo.setProductCode(priceDto.getProductCode());
                List<RemoteCouponGrantVo> couponGrant = couponGrantService.getCouponGrant(remoteCouponGrantBo);
                //如果起点城市没有匹配到 则查询终点城市的优惠卷
                if (ObjectUtils.isEmpty(couponGrant)){
                    remoteCouponGrantBo.setCityCode(calculateDTO.getEndCityCode());
                    couponGrant = couponGrantService.getCouponGrant(remoteCouponGrantBo);
                }
                priceDto.setCouponGrantVoList(couponGrant);
            }
        }
    }
}