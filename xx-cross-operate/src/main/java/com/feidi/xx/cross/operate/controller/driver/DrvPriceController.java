package com.feidi.xx.cross.operate.controller.driver;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;

import com.feidi.xx.cross.operate.domain.dto.EstimateRecordDTO;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.domain.vo.OprDrvCalculateVo;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import lombok.RequiredArgsConstructor;
import oracle.jdbc.proxy.annotation.GetProxy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机端 - 定价

 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.DRIVER_USER)
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX+"/pricing")
public class DrvPriceController extends BaseController {

    private final IOprPriceDetailService pricingService;

    /**
     * 根据条件计算价格
     * @param calculateDTO 计算价格条件
     * @return 计算价格结果
     */
    @Log(title = "定价", businessType = BusinessType.OTHER)
    @PostMapping("/calculatePrice")
    public R<OprCalculateVo> calculatePrice(@RequestBody OprCalculateDTO calculateDTO) {
        return R.ok(pricingService.calculatePrice(calculateDTO));
    }

    /**
     * 修改估价
     */
    @Log(title = "定价", businessType = BusinessType.UPDATE)
    @GetMapping("/updateEstimateRecord")
    public R<Boolean> updateEstimateRecord(EstimateRecordDTO estimateRecord) {
        return R.ok(pricingService.updateEstimateRecord(estimateRecord));
    }
    /**
     * 根据估价key查询计算司机收益
     * @param estimateRecord 查询条件
     * @return 价格详情
     */
    @Log(title = "定价", businessType = BusinessType.OTHER)
    @PostMapping("/getPriceKeyByIncome")
    public R<OprDrvCalculateVo> getPriceKeyByIncome(@RequestBody EstimateRecordDTO estimateRecord) {
        return R.ok(pricingService.getPriceKeyByIncome(estimateRecord));
    }

}
