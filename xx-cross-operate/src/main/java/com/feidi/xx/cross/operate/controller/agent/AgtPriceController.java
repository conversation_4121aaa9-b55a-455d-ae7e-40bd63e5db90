
package com.feidi.xx.cross.operate.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.dto.EstimateRecordDTO;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商 - 定价

 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.AGENT_USER)
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX+"/pricing")
public class AgtPriceController extends BaseController {

    private final IOprPriceDetailService pricingService;

    /**
     * 根据条件计算价格
     * @param calculateDTO 计算价格条件
     * @return 计算价格结果
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @PostMapping("/calculatePrice")
    public R<OprCalculateVo> calculatePrice(@RequestBody OprCalculateDTO calculateDTO) {
        return R.ok(pricingService.calculatePrice(calculateDTO));
    }

    /**
     * 修改估价
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @PostMapping("/updateEstimateRecord")
    public R<Boolean> updateEstimateRecord(@RequestBody EstimateRecordDTO estimateRecord) {
        return R.ok(pricingService.updateEstimateRecord(estimateRecord));
    }
}
