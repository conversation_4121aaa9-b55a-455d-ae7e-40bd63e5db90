package com.feidi.xx.cross.common.cache.market.constants;

import com.feidi.xx.common.core.constant.GlobalConstants;


/**
 * 订单服务 缓存常量
 *
 * <AUTHOR>
 */
public interface MarketCacheConstants {

    /**
     * 营销服务相关缓存前缀
     */
    String CACHE_PREFIX = "mkt:";

    /**
     * 代客下单次数枚举
     */
    String AGENT_ORDER_LIMIT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order-limit:";

    /**
     * 代客下单绑定乘客关系
     */
    String AGENT_ORDER_BIND_PASSENGER_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order-bind-passenger:";

    /**
     * 邀请关系配置
     */
    String INVITE_CONFIG_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "invite-config:";

    /**
     * 优惠券库存
     */
    String COUPON_STOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "coupon-stock:";


    /**
     * 优惠券用户领取记录
     */

    String COUPON_USER_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "user:";

    /**
     * 优惠券用户领取限制
     */
    String COUPON_USER_COUNT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "user-count:";

    String COUPON_STOCK_LOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "coupon-stock-lock:";

    /**
     * 定向放券，文件上传缓存
     */
    String TARGETED_COUPONS_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "targeted-coupons:";

    /**
     * 活动邀请码
     */
    String INVITE_CODE_TEMP_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "invite:code:temp:";


    /*
     * 该缓存用于存储邀请码的临时数据，通常在活动期间使用
     */
    String INVITE_SHARE_INFO_TEMP_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "invite:share:info:temp:";


    /**
     * 缓存社群二维码url的key
     */
    String COMMUNITY_QRCODE_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "community:qrcode:";
}
