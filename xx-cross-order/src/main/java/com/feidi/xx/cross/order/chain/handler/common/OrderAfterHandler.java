package com.feidi.xx.cross.order.chain.handler.common;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.enums.SmsUseEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.resource.api.RemoteVirtualPhoneService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 订单后置处理器（异步处理）
 * 处理一些非关键操作，可以异步处理的
 * 需要注意异步导致的租户ID丢失！！
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderAfterHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    private final OrdOrderMapper baseMapper;

    private final OrdOrderHelper ordOrderHelper;

    private final OrdCacheManager ordCacheManager;


    private final IOrdOrderProcessService ordOrderProcessService;

    private final OrdOrderProcessHelper ordOrderProcessHelper;


    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;

    @DubboReference
    private final RemoteVirtualPhoneService virtualPhoneService;


    @DubboReference
    private final RemoteCouponGrantService remoteCouponGrantService;

    /**
     * @param context 责任链执行入参
     */
    @Override
    public void handle(T context) {
        // 重新查一下，避免前面更新了字段
        OrdOrder order = baseMapper.selectById(context.getOrderId());
        Assert.notNull(order, StrUtil.format("后置处理器，订单{}不存在", context.getOrderId()));
        // 发送websocket消息，通知司机订单操作
        if (ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
            ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());
        }
        ExceptionUtil.ignoreEx(() -> {
            if (context instanceof OrderPlaceChainContext placeContext) {

                ExceptionUtil.ignoreEx(() -> {
                    // 获取跳转码
                    order.setCode(makeCode(order.getPassengerPhone()));
                    baseMapper.updateById(order);
                });

                ordOrderProcessHelper.afterPostOrder(order, placeContext.getOrderBo());
                //自动派单逻辑
                if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode())) {
                    if (ObjectUtils.isNotNull(placeContext.getDriverPhone()) && ObjectUtils.isNotNull(placeContext.getDriverId())) {
                        OrdOrderDispatchBo ordOrderDispatchBo = new OrdOrderDispatchBo();
                        ordOrderDispatchBo.setOrderId(placeContext.getOrderId());
                        ordOrderDispatchBo.setDriverId(placeContext.getDriverId());
                        ordOrderDispatchBo.setType(DispatchTypeEnum.AGENT_PROXY_ROB.getCode());
                        ordOrderDispatchBo.setUserType(UserTypeEnum.AUTO_USER.getUserType());
                        ordOrderProcessService.dispatchOrder(ordOrderDispatchBo);
                        log.info("下单成功，指派给手机号为:{}司机", placeContext.getDriverPhone());
                    }
                    //发送下单短信 新增 excel导入代下单 不发短信
                    if (StrUtil.isNotBlank(order.getCode()) && ((OrderPlaceChainContext) context).getOrderPrice() == null) {
                        //新增 勾选是否发送短信
                        if (placeContext.getSms() == null || placeContext.getSms()) {
                            RemoteOrderBo bo = placeContext.getOrderBo();
                            ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_NOTIFY, bo.getPassengerPhone(), bo.getEarliestTime(), bo.getEndPosition().getShortAddr(), order.getCode());

                        }
                    }
                }
                if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
                    // 推送消息
                    OrdOrderDispatchBo ordOrderDispatchBo = new OrdOrderDispatchBo();
                    ordOrderDispatchBo.setOrderId(placeContext.getOrderId());
                    ordOrderDispatchBo.setDriverId(placeContext.getDriverId());
                    ordOrderDispatchBo.setType(DispatchTypeEnum.DRIVER_PROXY_ROB.getCode());
                    ordOrderProcessService.dispatchOrder(ordOrderDispatchBo);
                    log.info("下单成功，订单code{}", order.getCode());
                    //发送下单短信
                    if (StrUtil.isNotBlank(order.getCode())) {
                        log.info("发送短信，订单code{}", order.getCode());
                        RemoteOrderBo bo = placeContext.getOrderBo();
                        ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_NOTIFY, bo.getPassengerPhone(), bo.getEarliestTime(), bo.getEndPosition().getShortAddr(), order.getCode());
                    }
                }

                if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.PASSENGER_QRCODE.getCode())) {
                    log.info("乘客扫码下单走入派单逻辑乘客1:{},司机{}", placeContext.getPassengerId(),placeContext.getDriverId());
                    // 推送消息
                    String inviteCode = ordCacheManager.getInviteCode(placeContext.getPassengerId());
                    log.info("司机id:{}",inviteCode);
                    if (ObjectUtil.isNotNull(ordCacheManager.getInviteCode(placeContext.getPassengerId()))) {
                        log.info("乘客扫码下单走入派单逻辑乘客2:{},司机{}", placeContext.getPassengerId(),placeContext.getDriverId());
                        ordCacheManager.delInviteCode(placeContext.getPassengerId());
                        OrdOrderDispatchBo ordOrderDispatchBo = new OrdOrderDispatchBo();
                        ordOrderDispatchBo.setOrderId(placeContext.getOrderId());
                        ordOrderDispatchBo.setDriverId(placeContext.getDriverId());
                        ordOrderDispatchBo.setType(DispatchTypeEnum.ASSIGN.getCode());
                        ordOrderProcessService.dispatchOrder(ordOrderDispatchBo);
                        //发送下单短信
                        RemoteOrderBo bo = placeContext.getOrderBo();
                        ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_PLACED, bo.getPassengerPhone(), bo.getEarliestTime(), bo.getEndPosition().getShortAddr());
                    }
                }
                log.info("下单成功，用户类型:{} 券id:{}", LoginHelper.getUserType().getUserType(), placeContext.getCouponGrantId());
                if (ObjectUtils.isNotNull(placeContext.getCouponGrantId()) && placeContext.getCouponGrantId() > 0) {
                    RemoteCouponGrantBo remoteCouponGrantBo = new RemoteCouponGrantBo();
                    remoteCouponGrantBo.setPassengerId(LoginHelper.getUserId());
                    remoteCouponGrantBo.setUsingStatus(CouponStatusEnum.USED.getCode());
                    remoteCouponGrantBo.setOrderId(order.getId());
                    remoteCouponGrantBo.setOrderNo(order.getOrderNo());
                    remoteCouponGrantBo.setId(placeContext.getCouponGrantId());
                    remoteCouponGrantService.updateStatus(remoteCouponGrantBo);
                }
            }
        }, "后置处理器异常", TenantHelper::clearDynamic);
    }

    /**
     * 生成订单code
     *
     * @param passengerPhone
     * @return
     */
    private String makeCode(String passengerPhone) {
        String code = OrderUtils.makeCode();
        OrdOrder existed = baseMapper.getByCode(code, passengerPhone);
        if (existed == null) {
            return code;
        }
        return makeCode(passengerPhone);
    }

}


