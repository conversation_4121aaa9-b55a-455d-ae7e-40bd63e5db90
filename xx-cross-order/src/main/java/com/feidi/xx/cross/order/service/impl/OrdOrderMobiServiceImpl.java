package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.InsureStatusEnum;
import com.feidi.xx.cross.common.enums.order.OrderDispatchStatusEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.utils.DateIntervalUtil;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChain;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainResult;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.MobiStatisticBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderDispatchQueryBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPlaceBo;
import com.feidi.xx.cross.order.domain.vo.MobiStatisticVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.domain.vo.dispatch.OrdOrderDispatchVo;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderInfoService;
import com.feidi.xx.cross.order.service.IOrdOrderMobiService;
import com.feidi.xx.cross.order.service.IOrdPositionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机端-订单服务接口实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderMobiServiceImpl implements IOrdOrderMobiService {

    private final OrderPlaceChain placeChain;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderMapper ordOrderMapper;
    private final IOrdPositionService ordPositionService;
    private final IOrdOrderInfoService ordOrderInfoService;

    @Override
    public MobiStatisticVo queryStatistic(MobiStatisticBo bo) {
        List<Date> dates = DateIntervalUtil.getDateRangeByInterval(bo.getInterval());
        LambdaQueryWrapper<OrdOrder> baseQ = Wrappers.<OrdOrder>lambdaQuery()
                .eq(bo.getAgentId() != null, OrdOrder::getAgentId, bo.getAgentId())
                .between(OrdOrder::getCreateTime, dates.get(0), dates.get(1));

        /*Long waitDispatch = ordOrderMapper.selectCount(baseQ.isNull(OrdOrder::getDispatchType));
        //Long dispatched = ordOrderMapper.selectCount(baseQ.isNotNull(OrdOrder::getDispatchType));
        Long finish = ordOrderMapper.selectCount(baseQ.isNotNull(OrdOrder::getFinishTime).eq(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode()));
        Long paid = ordOrderMapper.selectCount(baseQ.eq(OrdOrder::getPayStatus, PaymentStatusEnum.SUCCESS.getCode()));
        Long canceled = ordOrderMapper.selectCount(baseQ.isNotNull(OrdOrder::getCancelType));*/

        List<OrdOrder> orders = ordOrderMapper.selectList(baseQ);
        if (orders.isEmpty()) {
            return new MobiStatisticVo(0L, 0L, 0L, 0L, 0L);
        }
        long waitDispatch = orders.stream().filter(i -> IsYesEnum.NO.getCode().equals(i.getDispatch())).count();
        long dispatched = orders.stream().filter(i -> OrderStatusEnum.RECEIVE.getCode().equals(i.getStatus())
                || OrderStatusEnum.PICK.getCode().equals(i.getStatus())
                || OrderStatusEnum.PICK_START.getCode().equals(i.getStatus())
                || OrderStatusEnum.ING.getCode().equals(i.getStatus())).count();
        long finish = orders.stream().filter(i -> i.getFinishTime() != null && OrderStatusEnum.FINISH.getCode().equals(i.getStatus())).count();
        long paid = orders.stream().filter(i -> PaymentStatusEnum.SUCCESS.getCode().equals(i.getPayStatus())).count();
        long canceled = orders.stream().filter(i -> OrderStatusEnum.CANCEL.getCode().equals(i.getStatus())).count();

        return new MobiStatisticVo(waitDispatch, dispatched, finish, paid, canceled);
    }

    /**
     * 订单列表
     *
     * @param queryBo
     * @return
     */
    @Override
    public TableDataInfo<OrdOrderDispatchVo> queryList(OrdOrderDispatchQueryBo queryBo) {
        if (StringUtils.isNotBlank(queryBo.getOrderDispatchStatus())) {
            queryBo.setStatuses(OrderDispatchStatusEnum.getStatusListByCode(queryBo.getOrderDispatchStatus()));
            if (Objects.equals(queryBo.getOrderDispatchStatus(), OrderDispatchStatusEnum.COMPLAIN.getCode())) {
                queryBo.setComplain(IsYesEnum.YES.getCode());
            }
        }

        return TenantHelper.ignore(() -> {
            IPage<OrdOrderDispatchVo> iPage = ordOrderMapper.queryDispatchList(queryBo, queryBo.build());
            List<OrdOrderDispatchVo> records = iPage.getRecords();

            if (CollUtil.isNotEmpty(records)) {
                records.forEach(order -> {
                    order.setStatusText(OrderStatusEnum.getInfoByCode(order.getStatus()));
                    order.setCreateModelText(CreateModelEnum.getInfoByCode(order.getStatus()));
                    order.setPayStatusText(PaymentStatusEnum.getShowTextDrvByCode(order.getStatus()));
                    order.setProductCodeText(ProductCodeEnum.getInfoByCode(order.getProductCode()));
                });
                return TableDataInfo.build(records, iPage.getTotal());
            }
            return TableDataInfo.build(new ArrayList<>(), 0L);
        });
    }

    /**
     * 订单详情
     *
     * @param queryBo
     * @return
     */
    @Override
    public OrdOrderDispatchVo queryDetail(OrdOrderDispatchQueryBo queryBo) {

        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.select(OrdOrder::getId, OrdOrder::getOrderNo, OrdOrder::getLineId, OrdOrder::getDriverId, OrdOrder::getAgentId,
                OrdOrder::getPayStatus, OrdOrder::getPayTime, OrdOrder::getPassengerId, OrdOrder::getPassengerPhone,
                OrdOrder::getPassengerNum, OrdOrder::getPassengerRemark, OrdOrder::getOrderPrice, OrdOrder::getPayPrice,
                OrdOrder::getStatus, OrdOrder::getDue, OrdOrder::getEarliestTime, OrdOrder::getLatestTime, OrdOrder::getMileage,
                OrdOrder::getComplain, OrdOrder::getDispatch, OrdOrder::getDispatch, OrdOrder::getCreateModel,
                OrdOrder::getInsureStatus, OrdOrder::getInsureNo)
                .eq(OrdOrder::getId, queryBo.getId());
        OrdOrder order = ordOrderMapper.selectOne(lqw);

        OrdOrderDispatchVo orderDetail = BeanUtils.copyProperties(order, OrdOrderDispatchVo.class);
        orderDetail.setPassengerPhoneDes(DesensitizedUtil.mobilePhone(order.getPassengerPhone()));

        // 订单位置信息分组
        Map<String, OrdPositionVo> positionVoMap = ordPositionService.queryByOrderIds(Collections.singletonList(order.getId()))
                .stream().collect(Collectors.toMap(e -> e.getOrderId() + e.getType(), Function.identity(), (v1, v2) -> v1));

        // 订单关联信息
        OrdOrderInfo orderInfo = ordOrderInfoService.queryByOrderId(order.getId());
        if (orderInfo != null) {
            orderDetail.setCouponGrantQuota(orderInfo.getCouponGrantQuota());
            // 客诉信息
            if (Objects.equals(order.getComplain(), IsYesEnum.YES.getCode())) {
                orderDetail.setComplainPrice(orderInfo.getComplainPrice());
                orderDetail.setComplainTime(orderInfo.getComplainTime());
                orderDetail.setComplainReason(orderInfo.getComplainRemark());
            }
        }

        if (positionVoMap.containsKey(order.getId() + StartEndEnum.START.getCode())) {
            OrdPositionVo startPosition = positionVoMap.get(order.getId() + StartEndEnum.START.getCode());
            orderDetail.setStartProvince(startPosition.getProvince());
            orderDetail.setStartCity(startPosition.getCity());
            orderDetail.setStartDistrict(startPosition.getDistrict());
            orderDetail.setStartAddr(startPosition.getShortAddr());
            orderDetail.setStartAddrDes(DesensitizedUtil.address(startPosition.getShortAddr(), 5));
        }
        if (positionVoMap.containsKey(order.getId() + StartEndEnum.END.getCode())) {
            OrdPositionVo endPosition = positionVoMap.get(order.getId() + StartEndEnum.END.getCode());
            orderDetail.setEndProvince(endPosition.getProvince());
            orderDetail.setEndCity(endPosition.getCity());
            orderDetail.setEndDistrict(endPosition.getDistrict());
            orderDetail.setEndAddr(endPosition.getShortAddr());
            orderDetail.setEndAddrDes(DesensitizedUtil.address(endPosition.getShortAddr(), 5));
        }

        // 司机信息
        if (order.getDriverId() != null) {
            RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());
            if (orderDriverVo != null) {
                orderDetail.setDriverName(orderDriverVo.getDriverName());
                orderDetail.setDriverNameDes(DesensitizedUtil.chineseName(orderDriverVo.getDriverName()));
                orderDetail.setDriverPhone(orderDriverVo.getDriverPhone());
                orderDetail.setDriverPhoneDes(DesensitizedUtil.mobilePhone(orderDriverVo.getDriverPhone()));
                orderDetail.setCarNumber(orderDriverVo.getCarNumber());
            }
        }

        orderDetail.setStatusText(OrderStatusEnum.getInfoByCode(order.getStatus()));
        orderDetail.setCreateModelText(CreateModelEnum.getInfoByCode(order.getCreateModel()));
        orderDetail.setPayStatusText(PaymentStatusEnum.getShowTextDrvByCode(order.getPayStatus()));
        orderDetail.setInsureStatusText(InsureStatusEnum.getInfoByCode(order.getInsureStatus()));

        return orderDetail;
    }

    /**
     * 代客下单
     *
     * @param bo
     * @return
     */
    @Override
    public Long place(OrdOrderPlaceBo bo) {
        OrderPlaceChainResult result = placeChain.execute(BeanUtils.copyProperties(bo, OrderPlaceChainContext.class));
        return result.getId();
    }

    /**
     * 新增订单数量
     *
     * @param bo
     * @return
     */
    @Override
    public Map<String, Integer> addNum(OrdOrderDispatchQueryBo bo) {
        Map<String, Integer> resultMap = new HashMap<>();
        resultMap.put("allNumber", 0);
        resultMap.put("notDispatchNumber", 0);
        resultMap.put("dispatchNumber", 0);
        resultMap.put("finishNumber", 0);
        resultMap.put("complainNumber", 0);

        List<OrdOrder> orders = ordOrderMapper.selectList(buildQueryWrapper(bo));
        if (CollUtil.isNotEmpty(orders)) {
            resultMap.put("allNumber", orders.size());
            List<OrdOrder> notDispatch = StreamUtils.filter(orders, e -> !Objects.equals(e.getDispatch(), IsYesEnum.YES.getCode()));
            resultMap.put("notDispatchNumber", notDispatch.size());
            List<OrdOrder> dispatch = StreamUtils.filter(orders, e -> Objects.equals(e.getDispatch(), IsYesEnum.YES.getCode()));
            resultMap.put("dispatchNumber", dispatch.size());
            List<OrdOrder> finish = StreamUtils.filter(dispatch, e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()));
            resultMap.put("finishNumber", finish.size());
            List<OrdOrder> complain = StreamUtils.filter(dispatch, e -> Objects.equals(e.getComplain(), IsYesEnum.YES.getCode()));
            resultMap.put("complainNumber", complain.size());
        }
        return resultMap;
    }

    /**
     * 绑定查询参数
     *
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<OrdOrder> buildQueryWrapper(OrdOrderDispatchQueryBo bo) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(bo.getUnionId())) {
            lqw.nested(l -> {
                l.like(OrdOrder::getOrderNo, bo.getUnionId()).or()
                        .like(OrdOrder::getId, bo.getUnionId()).or()
                        .like(OrdOrder::getPlatformNo, bo.getUnionId()).or()
                        .like(OrdOrder::getInsureNo, bo.getUnionId()).or()
                        .like(OrdOrder::getPassengerPhone, bo.getUnionId()).or()
                        .like(OrdOrder::getStartAddr, bo.getUnionId()).or()
                        .like(OrdOrder::getEndAddr, bo.getUnionId()).or()
                        .like(OrdOrder::getDriverId, bo.getUnionId());
            });
        }
        if (CollUtil.isNotEmpty(bo.getLineIds()) && CollUtil.isNotEmpty(bo.getCityCodes())) {
            // ((line_id is null or line_id = 0) and (start_city_code in ('110000','120000') ) or line_id in (1,2,3))
            lqw.nested(l -> {
                l.nested(ll -> {
                            ll.apply("(line_id is null or line_id = 0)").in(OrdOrder::getStartCityCode, bo.getCityCodes());
                        }).or()
                        .in(OrdOrder::getLineId, bo.getLineIds());
            });
        } else if (CollUtil.isNotEmpty(bo.getLineIds())) {
            lqw.in(OrdOrder::getLineId, bo.getLineIds());
        } else if (CollUtil.isNotEmpty(bo.getCityCodes())) {
            lqw.apply("(line_id is null or line_id = 0)")
                    .in(OrdOrder::getStartCityCode, bo.getCityCodes());
        }
        lqw.select(OrdOrder::getId, OrdOrder::getCreateTime, OrdOrder::getStatus, OrdOrder::getDispatch, OrdOrder::getComplain)
                .eq(bo.getAgentId() != null, OrdOrder::getAgentId, bo.getAgentId())
                .gt(bo.getLastTime() != null, OrdOrder::getCreateTime, bo.getLastTime());
        return lqw;
    }
}
