package com.feidi.xx.cross.order.controller.agent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.ModuleTypeEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.core.validate.CancelGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.HighwayTypeEnum;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.operate.api.RemotePriceService;
import com.feidi.xx.cross.order.domain.OrdImportRecord;
import com.feidi.xx.cross.order.domain.bo.AgtOrderImportBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderRecordBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPlaceBo;
import com.feidi.xx.cross.order.domain.vo.*;
import com.feidi.xx.cross.order.excel.AgtOrderExcelListener;
import com.feidi.xx.cross.order.excel.ErrorExcelResult;
import com.feidi.xx.cross.order.excel.ExcelErrorLineResult;
import com.feidi.xx.cross.order.excel.ExcelLineResult;
import com.feidi.xx.cross.order.service.*;
import com.feidi.xx.cross.power.api.RemoteAgentCityService;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;
import com.feidi.xx.system.api.RemoteConfigService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商 - 订单
 * 前端访问路由地址为:/cross/order
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/order")
public class AgtOrderController extends BaseController {

    private final IOrdOrderService ordOrderService;
    private final IOrdOrderInsureService ordOrderInsureService;
    private final IOrdOrderProcessService ordOrderProcessService;
    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;
    @DubboReference
    private final RemoteAgentCityService remoteAgentCityService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    private final IOrdOrderMbrService orderMbrService;
    @DubboReference
    private final RemotePriceService remotePriceService;
    @DubboReference
    private final RemoteConfigService configService;

    private final IOrderImportRecordService orderImportRecordService;


    /**
     * 订单池
     */
    @Enum2TextAspect
    @PostMapping("/pool")
    public R<TableDataInfo<OrdOrderVo>> pool(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        Long agentId = LoginHelper.getAgentId();
        bo.setIsPool(true);
        bo.setDispatch(IsYesEnum.NO.getCode());
        // 订单是否被过滤
        bo.setFiltered(IsYesEnum.NO.getCode());
        // 代理商过滤
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
            agentId = bo.getAgentIds().get(0);
        } else {
            bo.setAgentId(agentId);
        }

        // 代理商开通城市
        Set<String> cityCodes = showCityOrder(agentId);
        if (CollUtils.isNotEmpty(cityCodes)) {
            bo.setCityCodes(new ArrayList<>(cityCodes));
        }
        // 代理商调度角色逻辑
        boolean aBoolean = handleDispatchRole(bo, cityCodes);
        if(aBoolean){
            return R.ok(TableDataInfo.build());
        }

        TableDataInfo<OrdOrderVo> tableDataInfo = ordOrderService.queryPageList(bo);
        Map<String, Object> extra = Collections.singletonMap("lastTime", new Date());
        tableDataInfo.setExtra(extra);
        return R.ok(tableDataInfo);
    }

    /**
     * 订单池-新加订单数
     */
    @Enum2TextAspect
    @PostMapping("/newNum")
    public R<Long> newNum(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        Long agentId = LoginHelper.getAgentId();
        // 是否订单池
        bo.setIsPool(true);
        // 未调度过
        bo.setDispatch(IsYesEnum.NO.getCode());
        // 订单是否被过滤
        bo.setFiltered(IsYesEnum.NO.getCode());
        // 代理商过滤
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
            agentId = bo.getAgentIds().get(0);
        } else {
            bo.setAgentId(agentId);
        }

        // 代理商开通城市
        Set<String> cityCodes = showCityOrder(agentId);
        if (CollUtils.isNotEmpty(cityCodes)) {
            bo.setCityCodes(new ArrayList<>(cityCodes));
        }
        // 代理商调度角色逻辑
        boolean aBoolean = handleDispatchRole(bo, cityCodes);
        if(aBoolean){
            return R.ok(0L);
        }

        return R.ok(ordOrderService.addNum(bo));
    }

    /**
     * 订单列表-统计新增订单数量
     */
    @Enum2TextAspect
    @PostMapping("/list/newNum")
    public R<Long> listNewNum(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        Long agentId = LoginHelper.getAgentId();
        // 订单是否被过滤
        bo.setFiltered(IsYesEnum.NO.getCode());
        // 代理商过滤
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
            agentId = bo.getAgentIds().get(0);
        } else {
            bo.setAgentId(agentId);
        }

        // 代理商开通城市
        Set<String> cityCodes = showCityOrder(agentId);
        if (CollUtils.isNotEmpty(cityCodes)) {
            bo.setCityCodes(new ArrayList<>(cityCodes));
        }
        // 代理商调度角色逻辑
        boolean aBoolean = handleDispatchRole(bo, cityCodes);
        if(aBoolean){
            return R.ok(0L);
        }

        return R.ok(ordOrderService.addNum(bo));
    }

    /**
     * 订单 默认：IsDispatch=Y
     */
    @Enum2TextAspect
    @PostMapping("/list")
    public TableDataInfo<OrdOrderVo> list(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        Long agentId = LoginHelper.getAgentId();
        // 订单是否被过滤
        bo.setFiltered(IsYesEnum.NO.getCode());
        // 代理商过滤
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
            agentId = bo.getAgentIds().get(0);
        } else {
            bo.setAgentId(agentId);
        }

        // 代理商开通城市
        Set<String> cityCodes = showCityOrder(agentId);
        if (CollUtils.isNotEmpty(cityCodes)) {
            bo.setCityCodes(new ArrayList<>(cityCodes));
        }
        //代理商调度角色逻辑
        boolean aBoolean = handleDispatchRole(bo, cityCodes);
        if(aBoolean){
            return TableDataInfo.build();
        }

        TableDataInfo<OrdOrderVo> tableDataInfo = ordOrderService.queryPageList(bo);
        Map<String, Object> extra = Collections.singletonMap("lastTime", new Date());
        tableDataInfo.setExtra(extra);
        return tableDataInfo;
    }

    /**
     * 代理商调度角色，查询分配线路
     * @param bo
     * @param cityCodes
     * @return
     */
    private boolean handleDispatchRole(OrdOrderQueryWebBo bo, Set<String> cityCodes) {
        String agentRoleType = LoginHelper.getLoginUser().getAgentRoleType();
        if (AgentRoleType.DISPATCH.getCode().equals(agentRoleType)) {
            if (CollUtils.isEmpty(bo.getLineIds())) {
                List<Long> agentUserLine = remoteAgentLineService.getAgentUserLine(LoginHelper.getUserId());
                bo.setLineIds(agentUserLine);
                return CollUtils.isEmpty(cityCodes) && CollUtils.isEmpty(agentUserLine);
            }
        } else {
            Long agentId = LoginHelper.getAgentId();
            HashSet<Long> agentLine = new HashSet<>(remoteAgentLineService.getAgentLine(agentId));
            if (CollUtil.isNotEmpty(agentLine)) {
                if (CollUtil.isNotEmpty(bo.getLineIds())) {
                    agentLine.retainAll(bo.getLineIds());
                }
                bo.setLineIds(agentLine);
            }
        }
        return false;
    }

    /**
     * 是否展示城市订单
     *
     * @param agentId 代理商id
     * @return 代理商开通城市集合
     */
    private Set<String> showCityOrder(Long agentId){
        //查询 代理信息+
        RemoteAgentVo agentInfoById = remoteAgentService.getAgentInfoById(agentId);

        if (IsYesEnum.YES.getCode().equals(agentInfoById.getShowCityOrder())) {
            return remoteAgentCityService.getAgentCityInfoByAgentId(agentId)
                    .stream()
                    .map(RemoteAgentCityVo::getCityCode)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }

        return Set.of();
    }


    /**
     * 导出订单列表
     */
    @PostMapping("/export")
    @Download(name = "订单列表", module = ModuleConstants.ORDER, mode = "no")
    public Object export(@RequestBody @Validated OrdOrderQueryWebBo bo, HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        // 订单是否被过滤
        bo.setFiltered(IsYesEnum.NO.getCode());
        // 代理商过滤
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
            agentId = bo.getAgentIds().get(0);
        } else {
            bo.setAgentId(agentId);
        }
        // 代理商开通城市
        Set<String> cityCodes = showCityOrder(agentId);
        if (CollUtils.isNotEmpty(cityCodes)) {
            bo.setCityCodes(new ArrayList<>(cityCodes));
        }
        // 代理商调度角色逻辑
        boolean aBoolean = handleDispatchRole(bo, cityCodes);
        if(aBoolean){
            return R.ok();
        }
        List<OrdOrderExportVo> list = ordOrderService.export(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<OrdOrderExportAgtVo> convert = MapstructUtils.convert(list, OrdOrderExportAgtVo.class);
        ExcelUtil.exportExcel(convert, "订单", OrdOrderExportAgtVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取订单详细信息
     *
     * @param id 主键
     */
    @Enum2TextAspect
    @GetMapping("/{id}")
    public R<OrdOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(ordOrderService.queryById(id));
    }

    /**
     * 调度
     */
    @Log(title = "代理商-订单调度", businessType = BusinessType.DISPATCH)
    @RepeatSubmit()
    @PutMapping("/dispatch")
    public R<Void> dispatch(@RequestBody OrdOrderDispatchBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        bo.setRemark("代理商调度");
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        Boolean flag = ordOrderProcessService.dispatchOrder(bo);
        return flag ? R.ok() : R.fail();
    }

    /**
     * 取消
     */
    @Log(title = "代理商-订单取消", businessType = BusinessType.CANCEL_ORDER)
    @RepeatSubmit()
    @PutMapping("/cancel")
    public R<Void> cancel(@Validated(CancelGroup.class) @RequestBody OrdOrderCancelBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(ordOrderProcessService.cancelOrder(bo));
    }

    @RepeatSubmit()
    @Log(title = "代理商-订单更新出发时间", businessType = BusinessType.UPDATE)
    @PostMapping("/updateEarliestTime")
    public R<Void> insure(@RequestBody OrdOrderHandleBaseBo handleBaseBo) {
        handleBaseBo.setUserId(LoginHelper.getUserId());
        handleBaseBo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        handleBaseBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        handleBaseBo.setRemark("代理商后台修改乘客出发时间");
        return toAjax(ordOrderService.updateEarliestTime(handleBaseBo));
    }

    /**
     * 投保
     */
    @RepeatSubmit()
    @Log(title = "代理商-订单投保", businessType = BusinessType.INSURE)
    @PutMapping("/insure/{orderId}")
    public R<Void> insure(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        OrdOrderHandleBaseBo bo = new OrdOrderHandleBaseBo();
        bo.setOrderId(orderId);
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        bo.setRemark("代理商操作投保");
        return toAjax(ordOrderInsureService.insure(bo));
    }

    /**
     * 代待客下单
     *
     * @param bo
     * @return
     */
    @Log(title = "代理商-待客下单", businessType = BusinessType.SEND_ORDER)
    @PostMapping("/place")
    public R<Long> place(@Validated @RequestBody OrdOrderPlaceBo bo) {
        bo.setPassengerId(null);
        bo.setCreateModel(CreateModelEnum.AGENT_ORDER.getCode());
        bo.setAgentId(LoginHelper.getAgentId());
        return R.ok(orderMbrService.place(bo));
    }

    /**
     * excel导入下单
     * @return 结果
     */
    @RepeatSubmit()
    @Log(title = "订单导入", businessType = BusinessType.OTHER)
    @PostMapping(value = "/importOrder",consumes = "multipart/form-data")
    public R<Object> importOrder(@RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("导入订单文件为空");
        }
        if (file.getSize() > 1024 * 1024 * 2) {
            return R.fail("导入订单文件大小不能超过2MB");
        }

        String s = configService.selectValueByKey(OrderConstants.WAIT_TIMEOUT_ORDER_RECORDING);
        if (StringUtils.isEmpty(s)){
            s = "0";
        }
        ErrorExcelResult<AgtOrderImportBo> excelResult;
        try (InputStream in = file.getInputStream()) {
            excelResult = (ErrorExcelResult<AgtOrderImportBo>) ExcelUtil.importExcel(in, AgtOrderImportBo.class, new AgtOrderExcelListener<>(true, remotePriceService, Integer.parseInt(s)));
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.fail("文件解析异常, 请联系管理员");
        }
        List<ExcelLineResult<AgtOrderImportBo>> list = excelResult.getLineResults();
        if (list.size() > 100) {
            return R.fail("单次导入订单不能超过100条，请修改后重试");
        }
        List<ExcelErrorLineResult<AgtOrderImportBo>> errorLineResults = excelResult.getErrorLineResults();
        int size = list.size();
        String fileName = ModuleTypeEnum.getInfoByCode(ModuleConstants.ORDER) + DateUtil.current();
        Date nowDate = DateUtils.getNowDate();
        List<OrdImportRecord> orderImportRecordList = new ArrayList<>();
        for (ExcelLineResult<AgtOrderImportBo> item : list) {
            try {
                orderMbrService.place(convert(item.getData()));
                OrdImportRecord orderImportRecord = getOrdImportRecord(item.getRowIndex(),item.getData(), fileName, nowDate);
                orderImportRecordList.add(orderImportRecord);
            } catch (ServiceException e) {
                int index = item.getRowIndex();
                String msg = e.getMessage();
                errorLineResults.add(new ExcelErrorLineResult<>(index, item.getData(), msg));
                size--;
            }
        }
        JSONObject object = new JSONObject();
        object.put("success", size);
        if (!errorLineResults.isEmpty()){
            String key = OrdCacheKeyEnum.ORD_ORDER_ERROR_EXCEL_KEY.create(LoginHelper.getUserId());
            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(errorLineResults), OrdCacheKeyEnum.ORD_ORDER_ERROR_EXCEL_KEY.getDuration());
            Map<Integer, String> collect = errorLineResults.stream().collect(Collectors.toMap(ExcelErrorLineResult::getRowIndex, ExcelErrorLineResult::getValidationErrors));
            object.put("fail", excelResult.getErrorLineResults().size());
            object.put("failReason", collect);
            for (ExcelErrorLineResult<AgtOrderImportBo> errorResult : errorLineResults) {
                OrdImportRecord orderImportRecord = getErrorOrdImportRecord(errorResult.getRowIndex(),errorResult.getData(), fileName, nowDate,errorResult.getValidationErrors());
                orderImportRecordList.add(orderImportRecord);
            }
            orderImportRecordService.importOrderList(orderImportRecordList);
            return R.fail(object.toString());
        }
        orderImportRecordService.importOrderList(orderImportRecordList);
        return R.ok(object.toString());
    }

    private OrdImportRecord getErrorOrdImportRecord(Integer rowIndex, AgtOrderImportBo data, String fileName, Date nowDate, String validationErrors) {
        OrdImportRecord ordImportRecord = getOrdImportRecord(rowIndex, data, fileName, nowDate);
        ordImportRecord.setHandleResult("1");
        ordImportRecord.setFailMsg(validationErrors);
        return ordImportRecord;
    }

    private static OrdImportRecord getOrdImportRecord(Integer rowIndex, AgtOrderImportBo data, String fileName, Date nowDate) {
        OrdImportRecord orderImportRecord =new OrdImportRecord();
        orderImportRecord.setRowIndex(rowIndex);
        orderImportRecord.setHandleResult("0");
        orderImportRecord.setStartAddress(data.getStartAddress());
        orderImportRecord.setEndAddress(data.getEndAddress());
        orderImportRecord.setStartTime(data.getStartTime());
        orderImportRecord.setPassengerNums(data.getPassengerNums());
        orderImportRecord.setOrderType(data.getOrderType());
        orderImportRecord.setOrderPrice(data.getOrderPrice().movePointRight(2).longValue());
        orderImportRecord.setPhone(data.getPhone());
        orderImportRecord.setRemark(data.getRemark());
        orderImportRecord.setAgentId(LoginHelper.getAgentId());
        orderImportRecord.setFileName(fileName);
        orderImportRecord.setCreateTime(nowDate);
        return orderImportRecord;
    }

    private OrdOrderPlaceBo convert(AgtOrderImportBo bo){
        OrdOrderPlaceBo opbo = new OrdOrderPlaceBo();
        opbo.setEstimateKey(bo.getEstimateKey());
        opbo.setOrderPrice(bo.getOrderPrice().movePointRight(2).longValue());
        opbo.setPassengerPhone(bo.getPhone());
        opbo.setPassengerRemark(bo.getRemark());
        opbo.setProductCode(ProductCodeEnum.getCodeByInfo(bo.getOrderType()));
        opbo.setCreateModel(CreateModelEnum.AGENT_ORDER.getCode());
        opbo.setHighwayType(HighwayTypeEnum.DRIVER.getCode());
        opbo.setAgentId(LoginHelper.getAgentId());
        return opbo;
    }

    /**
     * excel导入下单异常下载
     */
    @RepeatSubmit()
    @Log(title = "订单导入失败列表", businessType = BusinessType.EXPORT)
    @Download(name = "订单导入失败列表", module = ModuleConstants.ORDER, mode = "no")
    @PostMapping(value = "/exportExcel")
    public Object exportExcel() {
        String key = OrdCacheKeyEnum.ORD_ORDER_ERROR_EXCEL_KEY.create(LoginHelper.getUserId());
        if (!RedisUtils.hasKey(key)) {
            throw new ServiceException("失败数据文件已失效，请重新上传文件处理");
        }
        List<ExcelErrorLineResult> errorLineResults = JSONUtil.toList((String) RedisUtils.getCacheObject(key), ExcelErrorLineResult.class);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<AgtOrderImportVo> list = new ArrayList<>();
        errorLineResults.forEach(i -> list.add(convert(i)));
        ExcelUtil.exportExcel(list, "订单导入失败列表", AgtOrderImportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    private AgtOrderImportVo convert(ExcelErrorLineResult<AgtOrderImportBo> line){
        AgtOrderImportVo aoiv = BeanUtils.copyProperties(line.getData(), AgtOrderImportVo.class);
        aoiv.setRowIndex(line.getRowIndex());
        aoiv.setError(line.getValidationErrors());
        return aoiv;
    }

    /**
     * 查询导入订单记录
     *
     * @param pageQuery
     * @return
     */
    @RepeatSubmit()
    @Log(title = "查询导入订单记录")
    @PostMapping(value = "/orderRecordList")
    public TableDataInfo<OrdImportRecordVo> orderRecordList(@RequestBody PageQuery pageQuery) {
        Long agentId = LoginHelper.getAgentId();
        TableDataInfo<OrdImportRecordVo> queryImportList= orderImportRecordService.queryImportList(agentId,pageQuery);
        return queryImportList;
    }

    /**
     * 订单导入记录
     *
     * @param ordOrderRecordBo
     * @return
     */
    @RepeatSubmit()
    @Log(title = "订单导入记录", businessType = BusinessType.EXPORT)
    @Download(name = "订单导入记录",nameSpel="#ordOrderRecordBo.result == '0' ? '订单导入成功列表' : '订单导入失败列表'", module = ModuleConstants.ORDER, mode = "no")
    @PostMapping(value = "/downloadRecord")
    public Object downloadRecord(@RequestBody @Validated OrdOrderRecordBo ordOrderRecordBo) {
        if(null == ordOrderRecordBo.getAgentId()){
            ordOrderRecordBo.setAgentId(LoginHelper.getAgentId());
        }
        return orderImportRecordService.downloadRecord(ordOrderRecordBo);
    }
}
