package com.feidi.xx.cross.order.dubbo;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.order.api.RemoteOrdRecordService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrdRecordBo;
import com.feidi.xx.cross.order.domain.OrdOperate;
import com.feidi.xx.cross.order.domain.OrdRecord;
import com.feidi.xx.cross.order.mapper.OrdRecordMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 订单-通话录音记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-21
 */
@RequiredArgsConstructor
@DubboService
@Service
public class RemoteOrdRecordServiceImpl implements RemoteOrdRecordService {

    private final OrdRecordMapper baseMapper;

    /**
     * 新增订单-通话录音记录
     *
     * @param bo 订单-通话录音记录
     * @return 记录id
     */
    @Override
    public Long insertByBo(RemoteOrdRecordBo bo) {
        OrdRecord ordRecord = BeanUtils.copyProperties(bo, OrdRecord.class);
        baseMapper.insert(ordRecord);
        return ordRecord.getId();
    }

    @Override
    public boolean updateOssId(Long id, Long ossId) {
        return baseMapper.update(new LambdaUpdateWrapper<OrdRecord>()
                .eq(OrdRecord::getId, id)
                .set(OrdRecord::getRecordOssId, ossId)) > 0;
    }

}
