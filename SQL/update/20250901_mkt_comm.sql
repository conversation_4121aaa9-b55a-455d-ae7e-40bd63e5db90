SET
FOREIGN_KEY_CHECKS=0;

CREATE TABLE `mkt_community_campaign`
(
    `id`                 bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `activity_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动名称',
    `start_time`         datetime                                                      NOT NULL COMMENT '活动开始时间',
    `end_time`           datetime                                                      NOT NULL COMMENT '活动结束时间',
    `status`             char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NOT NULL DEFAULT '0' COMMENT '活动状态 0:待开始 1:进行中 2:已结束',
    `share_title`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分享标题',
    `receive_limit_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NOT NULL DEFAULT '1' COMMENT '领取限制 1:活动期间限领一次 2:每天限领一次',
    `coupon_ids`         json                                                          NOT NULL COMMENT '关联优惠券ID数组',
    `rule_content`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动规则',
    `create_dept`        bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`          bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`        datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`          bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`        datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`           char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                `idx_time_range`(`start_time` ASC, `end_time` ASC) USING BTREE,
    INDEX                `idx_status`(`status` ASC) USING BTREE,
    INDEX                `idx_tenant`(`tenant_id` ASC) USING BTREE,
    INDEX                `idx_start_status`(`start_time` ASC, `status` ASC) USING BTREE,
    INDEX                `idx_end_status`(`end_time` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社群活动主表' ROW_FORMAT = Dynamic;

CREATE TABLE `mkt_community_participation`
(
    `id`           bigint                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `activity_id`  bigint                                                   NOT NULL COMMENT '活动ID',
    `passenger_id` bigint                                                   NOT NULL COMMENT '用户ID',
    `phone`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     DEFAULT NULL COMMENT '手机号',
    `receive_time` datetime                                                 NOT NULL COMMENT '领取时间',
    `status`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '状态：1成功，0失败',
    `reason`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     DEFAULT NULL COMMENT '失败原因，比如超过领取限制',
    `tenant_id`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      DEFAULT NULL COMMENT '租户ID',
    `create_dept`  bigint                                                            DEFAULT NULL COMMENT '创建部门',
    `create_by`    bigint                                                            DEFAULT NULL COMMENT '创建者',
    `create_time`  datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by`    bigint                                                            DEFAULT NULL COMMENT '更新者',
    `update_time`  datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY            `idx_user_activity` (`passenger_id`,`activity_id`) USING BTREE,
    KEY            `idx_activity_time` (`activity_id`,`receive_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='活动领取记录表';
SET
FOREIGN_KEY_CHECKS=1;