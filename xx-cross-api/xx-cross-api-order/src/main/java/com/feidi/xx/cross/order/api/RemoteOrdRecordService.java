package com.feidi.xx.cross.order.api;


import com.feidi.xx.cross.order.api.domain.bo.RemoteOrdRecordBo;

/**
 * 订单虚拟号通话记录服务
 *
 * <AUTHOR>
 */
public interface RemoteOrdRecordService {

    /**
     * 新增记录
     * @param bo bo
     * @return recordId
     */
    Long insertByBo(RemoteOrdRecordBo bo);

    /**
     * 更新记录ossid
     * @param id id
     * @param ossId ossid
     * @return 结果
     */
    boolean updateOssId(Long id, Long ossId);
}
