package com.feidi.xx.cross.order.api;

import com.feidi.xx.cross.order.api.domain.bo.MobiDriverOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemoteOrderService {

    /**
     * 获取订单信息
     *
     * @param ids
     * @return
     */
    List<RemoteOrderVo> listByIds(Collection<Long> ids);

    /**
     * 统计待到账佣金
     *
     * @param driverId
     * @return
     */
    Long waitProfit(Long driverId);

    /**
     * 下单
     *
     * @param orderBo 下单参数
     * @return 下单结果
     */
    RemoteOrderVo placeOrder(RemoteOrderBo orderBo);

    /**
     * 美团通知派单
     *
     * @param handleBo 通知派单参数
     */
    Boolean confirmNotify(RemoteOrderHandleBo handleBo);

    /**
     * 订单取消费
     *
     * @param handleBo 订单取消费参数
     * @return 订单取消费
     */
    Long orderCancelFee(RemoteOrderHandleBo handleBo);

    /**
     * 取消订单
     *
     * @param handleBo 取消订单参数
     * @return 取消订单结果
     */
    boolean cancelOrder(RemoteOrderHandleBo handleBo);

    /**
     * 行程开始
     *
     * @param handleBo
     * @return
     */
    Boolean tripStart(RemoteOrderHandleBo handleBo);

    /**
     * 行程结束
     *
     * @param handleBo
     * @return
     */
    Boolean tripEnd(RemoteOrderHandleBo handleBo);

    /**
     * 根据平台编号和平台单号查询订单信息
     *
     * @param platformCode 平台编号
     * @param platformNo   平台单号
     * @return 订单信息
     */
    RemoteOrderVo queryByPlatformCodeAndPlatformNo(String platformCode, String platformNo);

    /**
     * 订单详情（仅订单信息，需要登录使用）
     *
     * @param orderId 订单ID
     * @return
     */
    RemoteOrderDetailVo queryById(Long orderId);

    /**
     * 根据司机id和订单状态获取订单列表
     *
     * @param driverId
     * @param orderStatuses
     * @return
     */
    List<RemoteOrderVo> queryByDriverIdAndStatuses(Long driverId, List<String> orderStatuses);

    /**
     * 更新订单资金流向状态
     *
     * @param driverId 司机id
     * @param newType  新状态
     * @param oldType  旧状态
     * @return
     */
    List<RemoteOrderDetailVo> updateOrderFlowStatus(Long driverId, String newType, String oldType);

    /**
     * 更新订单投保信息
     *
     * @return
     */
    Boolean updateOrderInsure(Long orderId, String insureNo);

    /**
     * 根据司机id获取未完成的订单
     *
     * @param driverId 司机id
     * @return 订单信息
     */
    List<RemoteOrderVo> queryNotFinishOrderByDriverId(Long driverId);

    /**
     * 根据订单编号查询订单信息
     *
     * @param orderNo 订单编号
     * @return 订单信息
     */
    RemoteOrderVo queryByOrderNo(String orderNo);

    /**
     * 支付确认
     * @param handleBo
     * @return
     */
    boolean paymentConfirm(RemoteOrderPaymentBo handleBo);

    /**
     * 根据乘客id查询订单
     *
     * @param passengerIds 乘客id
     * @return 订单信息
     */
    List<RemoteOrderVo> getOrderByPassengerIds(List<Long> passengerIds);

    /**
     * 根据司机id查询订单 司机出发接乘客-乘客上车（行程中）
     * @param driverId
     * @return
     */
    List<RemoteOrderVo> getOrderByDriverId(Long driverId);

    /**
     * 根据时间查询查询已结算且发生客诉的订单
     *
     * @param rebateStatus 结算状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单集合
     */
    List<RemoteOrderVo> queryByRebateStatusAndTimeWithComplaint(String rebateStatus, String startTime, String endTime);

    /**
     *  根据结算状态和结算时间查询订单
     *
     * @param rebateStatus 结算状态
     * @param startTime 结算开始时间
     * @param endTime 结算结束时间
     * @return 已结算订单集合
     */
    List<RemoteOrderVo> queryByRebateStatusAndTime(List<String> rebateStatus, String startTime, String endTime);

    /**
     * 根据订单完成时间和平台编码查询订单
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<RemoteOrderVo> queryByFinishTimeAndPlatformCodes(String startTime, String endTime, List<String> platformCodes);

    /**
     * 根据订单号查询订单基本信息
     *
     * @param orderNos 订单号列表
     * @return 订单基本信息列表
     */
    List<RemoteOrderVo> queryOrderByOrderNos(List<String> orderNos);

    /**
     * 根据订单的司机ids和订单状态查询订单数量
     *
     * @param driverIds 司机ids
     * @param orderStatuses 订单状态
     * @return
     */
    Map<Long, Long> queryOrderCountByDriverIdsAndStatuses(List<Long> driverIds, List<String> orderStatuses);

    /**
     * 根据乘客id查询第一个订单 OrderStatusEnum
     *
     * @param passengerId 乘客id
     * @param orderStatuses  订单状态
     * @return 订单信息
     */
    RemoteOrderVo queryFirstOrderByPassengerIdAndStatus(Long passengerId, List<String> orderStatuses);

    /**
     * 根据司机ids和订单状态获取订单列表
     *
     * bo
     * @return
     */
    Map<Long, List<RemoteOrderVo>> queryOrderByDriverIdsAndStatuses(MobiDriverOrderBo bo);

    /**
     * 根据司机ids和订单状态获取新增订单数
     *
     * bo
     * @return
     */
    Map<String, Integer> queryNewOrderNum(MobiDriverOrderBo bo);

    /**
     * 移动端查询司机位置订单数
     * @param driverIds
     * @param statusList
     * @param startTime
     * @param endTime
     * @param lines
     * @return
     */
    Map<Long, Long> queryPositionOrderNum(List<Long> driverIds, List<String> statusList, String startTime, String endTime, List<Long> lines);

    /**
     * 根据司机ids和订单状态查询订单
     *
     * @param driverIds
     * @return
     */
    List<RemoteOrderVo> queryByDriverIdsAndStatuses(List<Long> driverIds, List<String> orderStatuses);

    /**
     * 根据乘客id和订单状态查询订单数量
     *
     * @param passengerId
     * @param orderStatuses
     * @return
     */
    Long queryOrderNumByPassengerIdAndStatus(Long passengerId, List<String> orderStatuses);
}
